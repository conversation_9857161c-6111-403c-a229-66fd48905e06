// Google Clone Tutorial JavaScript
class GoogleCloneTutorial {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 10;
        this.completedSteps = new Set();
        
        this.initializeEventListeners();
        this.updateProgress();
        this.loadStepFromHash();
    }
    
    initializeEventListeners() {
        // Step navigation links
        document.querySelectorAll('.step-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const step = parseInt(e.target.closest('.step-link').dataset.step);
                this.goToStep(step);
            });
        });
        
        // Checklist items
        document.querySelectorAll('.checklist-item input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.handleChecklistChange(e);
            });
        });
        
        // Navigation buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-primary') && e.target.textContent.includes('Next')) {
                this.nextStep();
            } else if (e.target.matches('.btn-secondary') && e.target.textContent.includes('Previous')) {
                this.previousStep();
            }
        });
        
        // Handle browser back/forward
        window.addEventListener('hashchange', () => {
            this.loadStepFromHash();
        });
        
        // Auto-save progress to localStorage
        this.loadProgress();
    }
    
    goToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > this.totalSteps) return;
        
        // Hide current step
        document.querySelectorAll('.step-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // Show target step
        const targetStep = document.getElementById(`step${stepNumber}`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
        
        // Update navigation
        document.querySelectorAll('.step-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-step="${stepNumber}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
        
        // Update current step
        this.currentStep = stepNumber;
        
        // Update URL hash
        window.location.hash = `step${stepNumber}`;
        
        // Update progress
        this.updateProgress();
        
        // Update navigation buttons
        this.updateNavigationButtons();
        
        // Save progress
        this.saveProgress();
        
        // Scroll to top
        document.querySelector('.content-area').scrollTop = 0;
        
        // Show notification
        this.showNotification(`Moved to Step ${stepNumber}`, 'info');
    }
    
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.goToStep(this.currentStep + 1);
        }
    }
    
    previousStep() {
        if (this.currentStep > 1) {
            this.goToStep(this.currentStep - 1);
        }
    }
    
    updateNavigationButtons() {
        const currentStepContent = document.getElementById(`step${this.currentStep}`);
        if (!currentStepContent) return;
        
        const prevBtn = currentStepContent.querySelector('.btn-secondary');
        const nextBtn = currentStepContent.querySelector('.btn-primary');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentStep === 1;
            prevBtn.textContent = this.currentStep === 1 ? '← Previous' : '← Previous Step';
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentStep === this.totalSteps;
            nextBtn.textContent = this.currentStep === this.totalSteps ? 'Complete Tutorial' : 'Next Step →';
        }
    }
    
    handleChecklistChange(e) {
        const checklistItem = e.target.closest('.checklist-item');
        const stepNumber = this.currentStep;
        
        if (e.target.checked) {
            checklistItem.classList.add('completed');
            this.showNotification('Task completed! ✅', 'success');
        } else {
            checklistItem.classList.remove('completed');
        }
        
        // Check if all items in current step are completed
        const currentStepContent = document.getElementById(`step${stepNumber}`);
        const allCheckboxes = currentStepContent.querySelectorAll('.checklist-item input[type="checkbox"]');
        const checkedBoxes = currentStepContent.querySelectorAll('.checklist-item input[type="checkbox"]:checked');
        
        if (allCheckboxes.length === checkedBoxes.length && allCheckboxes.length > 0) {
            this.markStepCompleted(stepNumber);
        } else {
            this.completedSteps.delete(stepNumber);
        }
        
        this.updateStepNavigation();
        this.updateProgress();
        this.saveProgress();
    }
    
    markStepCompleted(stepNumber) {
        this.completedSteps.add(stepNumber);
        this.showNotification(`Step ${stepNumber} completed! 🎉`, 'success');
        
        // Update navigation link
        const stepLink = document.querySelector(`[data-step="${stepNumber}"]`);
        if (stepLink) {
            stepLink.classList.add('completed');
        }
    }
    
    updateStepNavigation() {
        document.querySelectorAll('.step-link').forEach(link => {
            const stepNumber = parseInt(link.dataset.step);
            if (this.completedSteps.has(stepNumber)) {
                link.classList.add('completed');
            } else {
                link.classList.remove('completed');
            }
        });
    }
    
    updateProgress() {
        const progressPercentage = (this.completedSteps.size / this.totalSteps) * 100;
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            progressFill.style.width = `${progressPercentage}%`;
        }
    }
    
    loadStepFromHash() {
        const hash = window.location.hash;
        if (hash) {
            const stepMatch = hash.match(/step(\d+)/);
            if (stepMatch) {
                const stepNumber = parseInt(stepMatch[1]);
                if (stepNumber >= 1 && stepNumber <= this.totalSteps) {
                    this.goToStep(stepNumber);
                }
            }
        }
    }
    
    saveProgress() {
        const progress = {
            currentStep: this.currentStep,
            completedSteps: Array.from(this.completedSteps),
            timestamp: Date.now()
        };
        localStorage.setItem('googleCloneTutorialProgress', JSON.stringify(progress));
    }
    
    loadProgress() {
        const saved = localStorage.getItem('googleCloneTutorialProgress');
        if (saved) {
            try {
                const progress = JSON.parse(saved);
                this.completedSteps = new Set(progress.completedSteps || []);
                
                // Restore completed checkboxes
                this.completedSteps.forEach(stepNumber => {
                    const stepContent = document.getElementById(`step${stepNumber}`);
                    if (stepContent) {
                        const checkboxes = stepContent.querySelectorAll('.checklist-item input[type="checkbox"]');
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = true;
                            checkbox.closest('.checklist-item').classList.add('completed');
                        });
                    }
                });
                
                this.updateStepNavigation();
                this.updateProgress();
            } catch (e) {
                console.warn('Failed to load tutorial progress:', e);
            }
        }
    }
    
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotification = document.querySelector('.tutorial-notification');
        if (existingNotification) {
            existingNotification.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = `tutorial-notification ${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 25px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            zIndex: '1000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        });
        
        // Set background color based on type
        const colors = {
            success: '#27ae60',
            warning: '#f39c12',
            info: '#3498db',
            error: '#e74c3c'
        };
        notification.style.background = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    resetProgress() {
        if (confirm('Are you sure you want to reset all progress? This cannot be undone.')) {
            localStorage.removeItem('googleCloneTutorialProgress');
            this.completedSteps.clear();
            
            // Uncheck all checkboxes
            document.querySelectorAll('.checklist-item input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.closest('.checklist-item').classList.remove('completed');
            });
            
            this.updateStepNavigation();
            this.updateProgress();
            this.goToStep(1);
            
            this.showNotification('Progress reset successfully!', 'info');
        }
    }
}

// Global functions for HTML onclick handlers
function nextStep() {
    if (window.googleTutorial) {
        window.googleTutorial.nextStep();
    }
}

function previousStep() {
    if (window.googleTutorial) {
        window.googleTutorial.previousStep();
    }
}

function resetProgress() {
    if (window.googleTutorial) {
        window.googleTutorial.resetProgress();
    }
}

// Initialize the tutorial when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.googleTutorial = new GoogleCloneTutorial();
    
    // Add some helpful console messages
    console.log('🔍 Google Clone Tutorial Loaded!');
    console.log('💡 Tips:');
    console.log('   • Complete checklist items to track progress');
    console.log('   • Use navigation buttons or sidebar to move between steps');
    console.log('   • Progress is automatically saved to localStorage');
    console.log('   • Type resetProgress() in console to reset all progress');
});
