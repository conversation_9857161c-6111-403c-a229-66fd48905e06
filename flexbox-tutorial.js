// Interactive Flexbox Tutorial JavaScript
class FlexboxTutorial {
    constructor() {
        this.flexContainer = document.getElementById('flexContainer');
        this.codeDisplay = document.getElementById('codeDisplay');
        this.selectedItem = null;
        this.itemCounter = 4;
        
        this.initializeEventListeners();
        this.updateCodeDisplay();
        this.updateAxisIndicators();
    }
    
    initializeEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });
        
        // Container property controls
        document.getElementById('flexDirection').addEventListener('change', (e) => {
            this.updateContainerProperty('flex-direction', e.target.value);
            this.updateAxisIndicators();
        });
        
        document.getElementById('flexWrap').addEventListener('change', (e) => {
            this.updateContainerProperty('flex-wrap', e.target.value);
        });
        
        document.getElementById('justifyContent').addEventListener('change', (e) => {
            this.updateContainerProperty('justify-content', e.target.value);
        });
        
        document.getElementById('alignItems').addEventListener('change', (e) => {
            this.updateContainerProperty('align-items', e.target.value);
        });
        
        document.getElementById('alignContent').addEventListener('change', (e) => {
            this.updateContainerProperty('align-content', e.target.value);
        });
        
        // Container size controls
        document.getElementById('containerHeight').addEventListener('input', (e) => {
            const height = e.target.value + 'px';
            this.flexContainer.style.minHeight = height;
            document.getElementById('heightValue').textContent = height;
            this.updateCodeDisplay();
        });
        
        document.getElementById('gap').addEventListener('input', (e) => {
            const gap = e.target.value + 'px';
            this.flexContainer.style.gap = gap;
            document.getElementById('gapValue').textContent = gap;
            this.updateCodeDisplay();
        });
        
        // Item property controls
        document.getElementById('flexGrow').addEventListener('input', (e) => {
            this.updateSelectedItemProperty('flex-grow', e.target.value);
        });
        
        document.getElementById('flexShrink').addEventListener('input', (e) => {
            this.updateSelectedItemProperty('flex-shrink', e.target.value);
        });
        
        document.getElementById('flexBasis').addEventListener('change', (e) => {
            this.updateSelectedItemProperty('flex-basis', e.target.value);
        });
        
        document.getElementById('alignSelf').addEventListener('change', (e) => {
            this.updateSelectedItemProperty('align-self', e.target.value);
        });
        
        document.getElementById('order').addEventListener('input', (e) => {
            this.updateSelectedItemProperty('order', e.target.value);
        });
        
        // Preset buttons
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.applyPreset(e.target.dataset.preset));
        });
        
        // Item selection
        this.addItemClickListeners();
    }
    
    addItemClickListeners() {
        document.querySelectorAll('.flex-item').forEach(item => {
            item.addEventListener('click', (e) => this.selectItem(e.target));
        });
    }
    
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }
    
    updateContainerProperty(property, value) {
        this.flexContainer.style[this.camelCase(property)] = value;
        this.updateCodeDisplay();
        
        // Add visual feedback
        this.flashContainer();
    }
    
    updateSelectedItemProperty(property, value) {
        if (!this.selectedItem) {
            this.showNotification('Please select a flex item first!', 'warning');
            return;
        }
        
        this.selectedItem.style[this.camelCase(property)] = value;
        this.updateCodeDisplay();
        
        // Add visual feedback
        this.flashItem(this.selectedItem);
    }
    
    selectItem(item) {
        // Remove previous selection
        document.querySelectorAll('.flex-item').forEach(i => i.classList.remove('selected'));
        
        // Select new item
        item.classList.add('selected');
        this.selectedItem = item;
        
        // Update UI
        document.getElementById('selectedItem').textContent = `Item ${item.dataset.item}`;
        
        // Update controls with current values
        this.updateItemControls(item);
        
        // Show notification
        this.showNotification(`Selected Item ${item.dataset.item}`, 'success');
    }
    
    updateItemControls(item) {
        const computedStyle = window.getComputedStyle(item);
        
        document.getElementById('flexGrow').value = item.style.flexGrow || '0';
        document.getElementById('flexShrink').value = item.style.flexShrink || '1';
        document.getElementById('flexBasis').value = item.style.flexBasis || 'auto';
        document.getElementById('alignSelf').value = item.style.alignSelf || 'auto';
        document.getElementById('order').value = item.style.order || '0';
    }
    
    applyPreset(preset) {
        const presets = {
            center: {
                'justify-content': 'center',
                'align-items': 'center'
            },
            'space-between': {
                'justify-content': 'space-between',
                'align-items': 'center'
            },
            column: {
                'flex-direction': 'column',
                'align-items': 'center'
            },
            wrap: {
                'flex-wrap': 'wrap',
                'justify-content': 'space-around'
            },
            reverse: {
                'flex-direction': 'row-reverse',
                'justify-content': 'space-between'
            }
        };
        
        const presetStyles = presets[preset];
        if (presetStyles) {
            Object.entries(presetStyles).forEach(([property, value]) => {
                this.flexContainer.style[this.camelCase(property)] = value;
                
                // Update corresponding select element
                const selectElement = document.getElementById(this.camelCase(property));
                if (selectElement) {
                    selectElement.value = value;
                }
            });
            
            this.updateCodeDisplay();
            this.updateAxisIndicators();
            this.flashContainer();
            this.showNotification(`Applied ${preset} preset!`, 'success');
        }
    }
    
    addItem() {
        this.itemCounter++;
        const newItem = document.createElement('div');
        newItem.className = 'flex-item';
        newItem.dataset.item = this.itemCounter;
        newItem.textContent = this.itemCounter;
        
        // Insert before axis indicators
        const mainAxis = document.getElementById('mainAxis');
        this.flexContainer.insertBefore(newItem, mainAxis);
        
        // Add click listener
        newItem.addEventListener('click', (e) => this.selectItem(e.target));
        
        this.showNotification(`Added Item ${this.itemCounter}!`, 'success');
        this.updateCodeDisplay();
    }
    
    removeItem() {
        const items = document.querySelectorAll('.flex-item');
        if (items.length > 1) {
            const lastItem = items[items.length - 1];
            if (lastItem === this.selectedItem) {
                this.selectedItem = null;
                document.getElementById('selectedItem').textContent = 'None';
            }
            lastItem.remove();
            this.showNotification('Removed last item!', 'info');
            this.updateCodeDisplay();
        } else {
            this.showNotification('Cannot remove the last item!', 'warning');
        }
    }
    
    resetItems() {
        // Reset all item styles
        document.querySelectorAll('.flex-item').forEach(item => {
            item.style.cssText = '';
            item.classList.remove('selected');
        });
        
        // Reset container styles
        this.flexContainer.style.cssText = '';
        this.flexContainer.style.minHeight = '300px';
        this.flexContainer.style.gap = '5px';
        
        // Reset all controls
        document.getElementById('flexDirection').value = 'row';
        document.getElementById('flexWrap').value = 'nowrap';
        document.getElementById('justifyContent').value = 'flex-start';
        document.getElementById('alignItems').value = 'stretch';
        document.getElementById('alignContent').value = 'stretch';
        document.getElementById('containerHeight').value = 300;
        document.getElementById('gap').value = 5;
        
        // Reset item controls
        document.getElementById('flexGrow').value = 0;
        document.getElementById('flexShrink').value = 1;
        document.getElementById('flexBasis').value = 'auto';
        document.getElementById('alignSelf').value = 'auto';
        document.getElementById('order').value = 0;
        
        this.selectedItem = null;
        document.getElementById('selectedItem').textContent = 'None';
        
        this.updateCodeDisplay();
        this.updateAxisIndicators();
        this.showNotification('Reset all properties!', 'info');
    }
    
    updateAxisIndicators() {
        const direction = this.flexContainer.style.flexDirection || 'row';
        const mainAxis = document.getElementById('mainAxis');
        const crossAxis = document.getElementById('crossAxis');
        
        if (direction === 'column' || direction === 'column-reverse') {
            mainAxis.textContent = 'Main Axis ↓';
            crossAxis.textContent = 'Cross Axis →';
        } else {
            mainAxis.textContent = 'Main Axis →';
            crossAxis.textContent = 'Cross Axis ↓';
        }
    }
    
    updateCodeDisplay() {
        const container = this.flexContainer;
        const computedStyle = window.getComputedStyle(container);
        
        let css = '<div class="comment">/* CSS Flexbox Code */</div>\n';
        css += '<div><span class="property">.flex-container</span> {</div>\n';
        css += '<div>&nbsp;&nbsp;<span class="property">display</span>: <span class="value">flex</span>;</div>\n';
        
        // Container properties
        const properties = [
            'flex-direction',
            'flex-wrap', 
            'justify-content',
            'align-items',
            'align-content'
        ];
        
        properties.forEach(prop => {
            const value = container.style[this.camelCase(prop)] || this.getDefaultValue(prop);
            css += `<div>&nbsp;&nbsp;<span class="property">${prop}</span>: <span class="value">${value}</span>;</div>\n`;
        });
        
        // Add gap if set
        if (container.style.gap) {
            css += `<div>&nbsp;&nbsp;<span class="property">gap</span>: <span class="value">${container.style.gap}</span>;</div>\n`;
        }
        
        // Add height if different from default
        if (container.style.minHeight && container.style.minHeight !== '300px') {
            css += `<div>&nbsp;&nbsp;<span class="property">min-height</span>: <span class="value">${container.style.minHeight}</span>;</div>\n`;
        }
        
        css += '<div>}</div>\n';
        
        // Add selected item styles
        if (this.selectedItem) {
            const item = this.selectedItem;
            const hasCustomStyles = item.style.flexGrow || item.style.flexShrink !== '1' || 
                                   item.style.flexBasis !== 'auto' || item.style.alignSelf !== 'auto' || 
                                   item.style.order !== '0';
            
            if (hasCustomStyles) {
                css += '\n<div class="comment">/* Selected Item Styles */</div>\n';
                css += `<div><span class="property">.flex-item:nth-child(${item.dataset.item})</span> {</div>\n`;
                
                if (item.style.flexGrow && item.style.flexGrow !== '0') {
                    css += `<div>&nbsp;&nbsp;<span class="property">flex-grow</span>: <span class="value">${item.style.flexGrow}</span>;</div>\n`;
                }
                if (item.style.flexShrink && item.style.flexShrink !== '1') {
                    css += `<div>&nbsp;&nbsp;<span class="property">flex-shrink</span>: <span class="value">${item.style.flexShrink}</span>;</div>\n`;
                }
                if (item.style.flexBasis && item.style.flexBasis !== 'auto') {
                    css += `<div>&nbsp;&nbsp;<span class="property">flex-basis</span>: <span class="value">${item.style.flexBasis}</span>;</div>\n`;
                }
                if (item.style.alignSelf && item.style.alignSelf !== 'auto') {
                    css += `<div>&nbsp;&nbsp;<span class="property">align-self</span>: <span class="value">${item.style.alignSelf}</span>;</div>\n`;
                }
                if (item.style.order && item.style.order !== '0') {
                    css += `<div>&nbsp;&nbsp;<span class="property">order</span>: <span class="value">${item.style.order}</span>;</div>\n`;
                }
                
                css += '<div>}</div>\n';
            }
        }
        
        this.codeDisplay.innerHTML = css;
    }
    
    getDefaultValue(property) {
        const defaults = {
            'flex-direction': 'row',
            'flex-wrap': 'nowrap',
            'justify-content': 'flex-start',
            'align-items': 'stretch',
            'align-content': 'stretch'
        };
        return defaults[property] || '';
    }
    
    camelCase(str) {
        return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
    }
    
    flashContainer() {
        this.flexContainer.style.borderColor = '#e74c3c';
        setTimeout(() => {
            this.flexContainer.style.borderColor = '#3498db';
        }, 200);
    }
    
    flashItem(item) {
        const originalTransform = item.style.transform;
        item.style.transform = 'scale(1.15)';
        setTimeout(() => {
            item.style.transform = originalTransform;
        }, 200);
    }
    
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 25px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            zIndex: '1000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            maxWidth: '300px'
        });
        
        // Set background color based on type
        const colors = {
            success: '#27ae60',
            warning: '#f39c12',
            info: '#3498db',
            error: '#e74c3c'
        };
        notification.style.background = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize the tutorial when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const tutorial = new FlexboxTutorial();
    
    // Add some helpful console messages
    console.log('🎯 Flexbox Tutorial Loaded!');
    console.log('💡 Tips:');
    console.log('   • Click on flex items to select and modify them');
    console.log('   • Use preset buttons for quick layouts');
    console.log('   • Watch the code display update in real-time');
    console.log('   • Try different combinations to see how they interact');
});

// Global functions for HTML onclick handlers
function addItem() {
    window.flexboxTutorial.addItem();
}

function removeItem() {
    window.flexboxTutorial.removeItem();
}

function resetItems() {
    window.flexboxTutorial.resetItems();
}

// Make tutorial instance globally accessible
document.addEventListener('DOMContentLoaded', () => {
    window.flexboxTutorial = new FlexboxTutorial();
});
