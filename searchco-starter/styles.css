:root{--bg:#fff;--text:#1f1f1f;--muted:#666;--primary:#1a73e8;--border:#e5e7eb;--focus:#0b57d0}
@media (prefers-color-scheme: dark){:root{--bg:#0b0b0b;--text:#eaeaea;--muted:#b3b3b3;--primary:#8ab4f8;--border:#222;--focus:#a8c7fa}}
*{box-sizing:border-box}
html,body{height:100%}
body{margin:0;font:16px/1.5 system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Cantarell,Noto Sans,sans-serif;background:var(--bg);color:var(--text)}
.visually-hidden{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0 0 0 0);white-space:nowrap;border:0}
.skip-link{position:absolute;left:-999px;top:auto}
.skip-link:focus{left:8px;top:8px;background:#fff;color:#000;padding:.5rem .75rem;border-radius:.25rem;box-shadow:0 2px 8px rgba(0,0,0,.2)}
.site-header{display:flex;justify-content:center;align-items:flex-end;height:35vh}
.logo{filter:drop-shadow(0 1px 0 rgba(0,0,0,.05))}
.home{display:flex;justify-content:center;align-items:flex-start;gap:1rem}
.search-form{display:flex;flex-direction:column;gap:.75rem;align-items:center;margin-inline:auto;width:min(600px,90vw)}
.search-form.inline{flex-direction:row;gap:.5rem;align-items:center;width:100%}
.input-wrap{display:flex;align-items:center;gap:.5rem;border:1px solid var(--border);border-radius:999px;padding:.5rem 1rem;background:rgba(0,0,0,.02)}
.search-form.inline .input-wrap{flex:1}
input[type=search]{flex:1;border:none;background:transparent;outline:none;min-width:0;font-size:1rem;color:var(--text)}
input[type=search]::placeholder{color:var(--muted)}
.actions{display:flex;gap:.5rem}
button{appearance:none;border:1px solid var(--border);background:#f8f9fa;color:#1f1f1f;padding:.6rem 1rem;border-radius:.5rem;cursor:pointer}
button:hover{background:#f1f3f4}
button:focus{outline:2px solid var(--focus);outline-offset:2px}
.results-header{display:flex;gap:1rem;align-items:center;padding:.75rem 1rem;border-bottom:1px solid var(--border)}
.results{max-width:720px;margin:1rem auto;padding:0 1rem}
.summary{color:var(--muted);margin:.5rem 0 1rem}
.results-list{list-style:none;margin:0;padding:0;display:grid;gap:1rem}
.result{display:grid;gap:.25rem}
.result a{color:var(--primary);text-decoration:none}
.result a:hover{text-decoration:underline}
.result .url{color:var(--muted);font-size:.85rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
.site-footer{border-top:1px solid var(--border);padding:1rem;color:var(--muted)}
.site-footer .footer-links{list-style:none;display:flex;gap:1rem;justify-content:center;margin:0;padding:0}
.site-footer.small{font-size:.9rem}
.hint{color:var(--muted);font-size:.9rem;margin:0}

