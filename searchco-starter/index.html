<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>SearchCo</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <a class="skip-link" href="#main">Skip to main content</a>
  <header class="site-header" role="banner">
    <img src="assets/logo.svg" class="logo" alt="SearchCo logo" width="120" height="40">
  </header>

  <main id="main" class="home" role="main">
    <form class="search-form" method="get" action="results.html" role="search">
      <label class="visually-hidden" for="q">Search</label>
      <div class="input-wrap">
        <input id="q" name="q" type="search" inputmode="search" autocomplete="off" placeholder="Search the web" required>
      </div>
      <div class="actions">
        <button type="submit">Search</button>
        <button type="button" id="luckyBtn" aria-describedby="lucky-hint">I'm Feeling Lucky</button>
      </div>
      <p id="lucky-hint" class="hint">Press Enter to search, or try “lucky” to jump to the first result.</p>
    </form>
  </main>

  <footer class="site-footer" role="contentinfo">
    <nav aria-label="Footer">
      <ul class="footer-links">
        <li><a href="#">About</a></li>
        <li><a href="#">Privacy</a></li>
        <li><a href="#">Terms</a></li>
      </ul>
    </nav>
  </footer>

  <script src="script.js" defer></script>
  <script>
    // "Lucky" button: navigate to results with a special flag
    document.addEventListener('DOMContentLoaded', () => {
      const lucky = document.getElementById('luckyBtn');
      const input = document.getElementById('q');
      lucky.addEventListener('click', () => {
        const q = input.value.trim();
        if (!q) return;
        window.location.href = `results.html?q=${encodeURIComponent(q)}&lucky=1`;
      });
    });
  </script>
</body>
</html>

