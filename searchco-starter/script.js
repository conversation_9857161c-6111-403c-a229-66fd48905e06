// Shared utilities for SearchCo starter
(function(){
  const $ = (sel, root=document) => root.querySelector(sel);
  const $$ = (sel, root=document) => Array.from(root.querySelectorAll(sel));

  function getQuery(){
    const params = new URLSearchParams(location.search);
    return {
      q: (params.get('q')||'').trim(),
      lucky: params.get('lucky') === '1'
    };
  }

  function highlight(text, term){
    if(!term) return text;
    try {
      const re = new RegExp(`(${escapeRegExp(term)})`, 'ig');
      return text.replace(re, '<mark>$1</mark>');
    } catch(e){
      return text;
    }
  }

  function escapeRegExp(s){
    return s.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  async function fetchIndex(){
    const res = await fetch('data/search-index.json');
    if(!res.ok) throw new Error('Failed to load dataset');
    return res.json();
  }

  function searchData(items, q){
    if(!q) return [];
    const lc = q.toLowerCase();
    return items
      .map(item => {
        let score = 0;
        const fields = ['title','url','description','tags'];
        for(const f of fields){
          const v = (item[f] || '').toString().toLowerCase();
          if(!v) continue;
          const idx = v.indexOf(lc);
          if(idx !== -1){
            score += (f==='title'?5:(f==='tags'?3:1)) + Math.max(0, 3 - idx/20);
          }
        }
        return {...item, _score: score};
      })
      .filter(x => x._score > 0)
      .sort((a,b) => b._score - a._score)
      .slice(0, 50);
  }

  async function initResults(){
    if(!$('.results')) return; // only on results page
    const { q, lucky } = getQuery();
    const input = $('#q');
    if(input) input.value = q;

    const summary = $('#summary');
    const list = $('#results');

    if(!q){
      summary.textContent = 'Please enter a query.';
      return;
    }

    try{
      const data = await fetchIndex();
      const matches = searchData(data.items || data, q);

      if(lucky && matches[0]){
        location.replace(matches[0].url);
        return;
      }

      summary.textContent = `${matches.length} results for "${q}"`;
      list.innerHTML = matches.map(item => `
        <li class="result">
          <div class="url">${item.url.replace(/^https?:\\/\\//,'')}</div>
          <a href="${item.url}" target="_blank" rel="noopener">${highlight(item.title, q)}</a>
          <div class="snippet">${highlight(item.description, q)}</div>
        </li>`).join('');
    } catch(err){
      summary.textContent = 'Error loading results. Please try again.';
      console.error(err);
    }
  }

  // Run when DOM ready
  document.addEventListener('DOMContentLoaded', initResults);
})();

