{"items": [{"title": "MDN Web Docs", "url": "https://developer.mozilla.org/", "description": "Resources for developers, by developers. Learn about web technologies like HTML, CSS, and JavaScript.", "tags": "docs web html css js"}, {"title": "W3C HTML Specification", "url": "https://www.w3.org/TR/html/", "description": "The HTML Living Standard and specifications maintained by the World Wide Web Consortium.", "tags": "standards html spec"}, {"title": "CSS-Tricks Guide to Flexbox", "url": "https://css-tricks.com/snippets/css/a-guide-to-flexbox/", "description": "An illustrated guide to CSS Flexbox layout with examples.", "tags": "css layout flexbox"}, {"title": "JavaScript Info", "url": "https://javascript.info/", "description": "Modern JavaScript tutorial covering basics to advanced topics.", "tags": "javascript tutorial es6"}, {"title": "Web.dev", "url": "https://web.dev/", "description": "Best practices for building modern websites: performance, accessibility, and more.", "tags": "performance accessibility pwa"}, {"title": "SearchCo: Introduction", "url": "https://example.com/searchco", "description": "A fictional brand used for search engine UI exercises.", "tags": "searchco demo"}]}