SearchCo Starter Kit

A minimal, accessible starter kit to teach CS students how to build a Google-like search experience using only HTML, CSS, and vanilla JavaScript.

Quick start
- Open index.html in a browser, or serve the folder with any static server.
- Type a query and submit. You’ll be routed to results.html?q=...
- Results page filters a small local dataset in data/search-index.json.

Structure
- index.html            Homepage with centered logo and search form
- results.html          Results page that reads ?q= and renders matches
- styles.css            Shared styles with light/dark theme variables
- script.js             JS used by both pages (query parsing, render utils)
- data/search-index.json  Sample dataset (editable)
- assets/logo.svg       Placeholder logo (replace with your own)

What students learn
- Semantic HTML and accessible forms
- Reading URLSearchParams and updating the DOM
- Fetching and filtering local JSON, rendering lists
- Basic responsive design and theme variables

Next steps
- Improve ranking and highlighting
- Add autocomplete (Project 6)
- Add PWA features (optional)

License: MIT (education-friendly)
