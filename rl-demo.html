<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reinforcement Learning Demo - Q-Learning Grid World</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            padding: 30px;
        }

        .demo-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .grid-container {
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(8, 60px);
            grid-template-rows: repeat(6, 60px);
            gap: 2px;
            border: 3px solid #2c3e50;
            border-radius: 10px;
            padding: 10px;
            background: #2c3e50;
        }

        .cell {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .cell.empty {
            background: #ecf0f1;
            color: #7f8c8d;
        }

        .cell.wall {
            background: #34495e;
            color: white;
        }

        .cell.start {
            background: #27ae60;
            color: white;
        }

        .cell.goal {
            background: #e74c3c;
            color: white;
        }

        .cell.agent {
            background: #3498db;
            color: white;
            animation: pulse 1s infinite;
        }

        .cell.visited {
            background: #f39c12;
            opacity: 0.7;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .q-value {
            position: absolute;
            font-size: 8px;
            font-weight: normal;
        }

        .q-up { top: 2px; left: 50%; transform: translateX(-50%); }
        .q-down { bottom: 2px; left: 50%; transform: translateX(-50%); }
        .q-left { left: 2px; top: 50%; transform: translateY(-50%); }
        .q-right { right: 2px; top: 50%; transform: translateY(-50%); }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .info-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .info-panel h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .stat {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .stat:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .stat-value {
            color: #3498db;
            font-weight: bold;
        }

        .parameters {
            display: grid;
            gap: 15px;
        }

        .param-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .param-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .param-group input {
            padding: 8px 12px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .param-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .grid {
                grid-template-columns: repeat(8, 50px);
                grid-template-rows: repeat(6, 50px);
            }
            
            .cell {
                width: 50px;
                height: 50px;
                font-size: 10px;
            }
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: repeat(8, 40px);
                grid-template-rows: repeat(6, 40px);
            }
            
            .cell {
                width: 40px;
                height: 40px;
                font-size: 8px;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Reinforcement Learning Demo</h1>
            <p>Interactive Q-Learning Algorithm in Grid World Environment</p>
        </div>

        <div class="main-content">
            <div class="demo-area">
                <div class="controls">
                    <button class="btn btn-primary" id="startBtn">▶️ Start Training</button>
                    <button class="btn btn-warning" id="pauseBtn">⏸️ Pause</button>
                    <button class="btn btn-danger" id="resetBtn">🔄 Reset</button>
                    <button class="btn btn-success" id="stepBtn">👣 Single Step</button>
                    <label>
                        Speed: <input type="range" id="speedSlider" min="1" max="100" value="50" style="margin-left: 10px;">
                    </label>
                </div>

                <div class="grid-container">
                    <div class="grid" id="grid"></div>
                </div>
            </div>

            <div class="sidebar">
                <div class="info-panel">
                    <h3>📊 Training Statistics</h3>
                    <div class="stat">
                        <span class="stat-label">Episode:</span>
                        <span class="stat-value" id="episode">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Steps:</span>
                        <span class="stat-value" id="steps">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Total Reward:</span>
                        <span class="stat-value" id="totalReward">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Success Rate:</span>
                        <span class="stat-value" id="successRate">0%</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Epsilon:</span>
                        <span class="stat-value" id="epsilonValue">1.0</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                </div>

                <div class="info-panel">
                    <h3>⚙️ Parameters</h3>
                    <div class="parameters">
                        <div class="param-group">
                            <label>Learning Rate (α):</label>
                            <input type="number" id="learningRate" value="0.1" min="0" max="1" step="0.01">
                        </div>
                        <div class="param-group">
                            <label>Discount Factor (γ):</label>
                            <input type="number" id="discountFactor" value="0.9" min="0" max="1" step="0.01">
                        </div>
                        <div class="param-group">
                            <label>Epsilon (ε):</label>
                            <input type="number" id="epsilon" value="1.0" min="0" max="1" step="0.01">
                        </div>
                        <div class="param-group">
                            <label>Epsilon Decay:</label>
                            <input type="number" id="epsilonDecay" value="0.995" min="0.9" max="1" step="0.001">
                        </div>
                    </div>
                </div>

                <div class="info-panel">
                    <h3>🎨 Legend</h3>
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #27ae60;"></div>
                            <span>Start</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #e74c3c;"></div>
                            <span>Goal</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #3498db;"></div>
                            <span>Agent</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #34495e;"></div>
                            <span>Wall</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #f39c12;"></div>
                            <span>Visited</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #ecf0f1;"></div>
                            <span>Empty</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="rl-demo.js"></script>
</body>
</html>
