<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python Functions - Step by Step Course</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px 50px;
            max-width: 1100px;
            width: 92%;
            max-height: 88vh;
            overflow-y: auto;
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
        }

        h2 {
            color: #2d3748;
            font-size: 2em;
            margin-bottom: 25px;
            text-align: center;
        }

        h3 {
            color: #4a5568;
            font-size: 1.5em;
            margin-bottom: 20px;
            margin-top: 30px;
        }

        p, li {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 1em;
            margin: 20px 0;
            overflow-x: auto;
            border-left: 4px solid #667eea;
            line-height: 1.6;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .code-block::before {
            content: "Python";
            position: absolute;
            top: 8px;
            right: 12px;
            background: #667eea;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.75em;
            font-weight: bold;
            opacity: 0.8;
        }

        .highlight {
            background: #fef5e7;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #f6ad55;
            margin: 20px 0;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            color: #4a5568;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .exercise {
            background: #e6fffa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #38b2ac;
            margin: 20px 0;
        }

        .solution {
            background: #f0fff4;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #48bb78;
            margin: 20px 0;
            display: none;
        }

        .show-solution {
            background: #48bb78;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }

        /* Syntax highlighting */
        .keyword {
            color: #ff6b6b;
            font-weight: bold;
        }

        .string {
            color: #51cf66;
        }

        .comment {
            color: #868e96;
            font-style: italic;
        }

        /* Better inline code */
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #495057;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .slide {
                padding: 25px;
                width: 95%;
            }
            
            .code-block {
                font-size: 0.9em;
                padding: 15px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            h2 {
                font-size: 1.6em;
            }
            
            .nav-btn {
                padding: 10px 20px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">28</span>
        </div>

        <!-- Slide 1: Title Slide -->
        <div class="slide active">
            <h1>Python Functions</h1>
            <h2>A Step-by-Step Course</h2>
            <div style="text-align: center; margin-top: 50px;">
                <p style="font-size: 1.4em; color: #4a5568;">Master the fundamentals of Python functions</p>
                <p style="font-size: 1.1em; color: #718096; margin-top: 20px;">From basic syntax to advanced concepts</p>
            </div>
            <div class="highlight">
                <p><strong>What you'll learn:</strong></p>
                <ul>
                    <li>Function definition and calling</li>
                    <li>Parameters and arguments</li>
                    <li>Return values and scope</li>
                    <li>Advanced function concepts</li>
                    <li>Best practices and real examples</li>
                </ul>
            </div>
        </div>

        <!-- Slide 2: What are Functions? -->
        <div class="slide">
            <h2>What are Functions?</h2>
            <p>A <strong>function</strong> is a reusable block of code that performs a specific task.</p>
            
            <h3>Think of functions like:</h3>
            <ul>
                <li><strong>A recipe</strong> - You give it ingredients (inputs) and get a dish (output)</li>
                <li><strong>A machine</strong> - You put something in, it processes it, and gives you a result</li>
                <li><strong>A tool</strong> - You use it whenever you need to perform a specific task</li>
            </ul>

            <div class="highlight">
                <p><strong>Real-world analogy:</strong></p>
                <p>A coffee machine is like a function:</p>
                <ul>
                    <li><strong>Input:</strong> Coffee beans, water, settings</li>
                    <li><strong>Process:</strong> Brewing</li>
                    <li><strong>Output:</strong> A cup of coffee</li>
                </ul>
            </div>

            <div class="code-block"># This is what a simple function looks like
<span class="keyword">def</span> make_coffee(beans, water):
    # Process the inputs
    coffee = brew(beans, water)
    <span class="keyword">return</span> coffee</div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="nav-btn" id="prev-btn" onclick="changeSlide(-1)">← Previous</button>
            <button class="nav-btn" id="next-btn" onclick="changeSlide(1)">Next →</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                changeSlide(1);
            } else if (e.key === 'ArrowLeft') {
                changeSlide(-1);
            }
        });

        // Initialize
        showSlide(0);

        // Solution toggle function
        function toggleSolution(button) {
            const solution = button.nextElementSibling;
            if (solution.style.display === 'none' || solution.style.display === '') {
                solution.style.display = 'block';
                button.textContent = 'Hide Solution';
            } else {
                solution.style.display = 'none';
                button.textContent = 'Show Solution';
            }
        }
    </script>
</body>
</html>
