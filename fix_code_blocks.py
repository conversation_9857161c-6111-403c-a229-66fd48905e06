#!/usr/bin/env python3
"""
Script to fix code block formatting in the HTML file
"""

import re

def fix_code_blocks(content):
    """Fix code block formatting by removing extra whitespace and improving structure"""
    
    # Pattern to match code blocks
    code_block_pattern = r'<div class="code-block">(.*?)</div>'
    
    def fix_single_block(match):
        code_content = match.group(1)
        
        # Remove leading/trailing whitespace from each line
        lines = code_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Skip empty lines at the beginning and end
            if line.strip() or cleaned_lines:
                cleaned_lines.append(line.rstrip())
        
        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()
        
        # Join lines back together
        fixed_content = '\n'.join(cleaned_lines)
        
        return f'<div class="code-block">{fixed_content}</div>'
    
    # Apply the fix to all code blocks
    fixed_content = re.sub(code_block_pattern, fix_single_block, content, flags=re.DOTALL)
    
    return fixed_content

def main():
    # Read the HTML file
    with open('python-functions-course.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix the code blocks
    fixed_content = fix_code_blocks(content)
    
    # Write back to file
    with open('python-functions-course.html', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("Code blocks have been fixed!")

if __name__ == "__main__":
    main()
