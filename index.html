<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reinforcement Learning Demos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .demos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            padding: 40px;
        }

        .demo-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .demo-card:hover::before {
            left: 100%;
        }

        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .demo-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .demo-card h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .demo-card p {
            color: #5a6c7d;
            line-height: 1.6;
            margin-bottom: 25px;
            font-size: 1rem;
        }

        .features {
            list-style: none;
            margin-bottom: 25px;
            text-align: left;
        }

        .features li {
            color: #27ae60;
            margin-bottom: 8px;
            position: relative;
            padding-left: 25px;
        }

        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            font-weight: bold;
            color: #27ae60;
        }

        .demo-btn {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            background: linear-gradient(135deg, #2980b9, #3498db);
        }

        .demo-btn.advanced {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .demo-btn.advanced:hover {
            background: linear-gradient(135deg, #c0392b, #e74c3c);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .info-section {
            background: #ecf0f1;
            padding: 40px;
            text-align: center;
        }

        .info-section h3 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .info-item {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .info-item h4 {
            color: #3498db;
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .info-item p {
            color: #5a6c7d;
            line-height: 1.5;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .footer p {
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .tech-item {
            background: rgba(255,255,255,0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .demos-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .demo-card {
                padding: 20px;
            }
            
            .info-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Reinforcement Learning Demos</h1>
            <p>Interactive demonstrations of popular RL algorithms including Q-Learning, SARSA, and Deep Q-Networks</p>
        </div>

        <div class="demos-grid">
            <div class="demo-card">
                <span class="demo-icon">🎯</span>
                <h2>Basic Q-Learning Demo</h2>
                <p>Learn the fundamentals of reinforcement learning with an interactive Q-Learning implementation in a grid world environment.</p>
                
                <ul class="features">
                    <li>Interactive Q-Learning algorithm</li>
                    <li>Visual Q-value display</li>
                    <li>Real-time training statistics</li>
                    <li>Adjustable hyperparameters</li>
                    <li>Step-by-step execution</li>
                    <li>Mobile-responsive design</li>
                </ul>
                
                <a href="rl-demo.html" class="demo-btn">Launch Basic Demo</a>
            </div>

            <div class="demo-card">
                <span class="demo-icon">🧠</span>
                <h2>Advanced Multi-Algorithm Demo</h2>
                <p>Compare multiple RL algorithms side-by-side with advanced visualizations, multiple environments, and performance analytics.</p>
                
                <ul class="features">
                    <li>Q-Learning, SARSA & DQN algorithms</li>
                    <li>Multiple environments (Maze, Cliff, Windy)</li>
                    <li>Real-time performance charts</li>
                    <li>Algorithm comparison tools</li>
                    <li>Advanced hyperparameter tuning</li>
                    <li>Neural network visualization</li>
                </ul>
                
                <a href="advanced-rl-demo.html" class="demo-btn advanced">Launch Advanced Demo</a>
            </div>
        </div>

        <div class="info-section">
            <h3>🎓 What You'll Learn</h3>
            <div class="info-grid">
                <div class="info-item">
                    <h4>Q-Learning</h4>
                    <p>Understand off-policy temporal difference learning and how agents learn optimal action-value functions through exploration and exploitation.</p>
                </div>
                <div class="info-item">
                    <h4>SARSA</h4>
                    <p>Explore on-policy learning where the agent learns the value of the policy it's currently following, leading to more conservative behavior.</p>
                </div>
                <div class="info-item">
                    <h4>Deep Q-Networks</h4>
                    <p>Discover how neural networks can approximate Q-functions for complex state spaces, enabling RL in high-dimensional environments.</p>
                </div>
                <div class="info-item">
                    <h4>Hyperparameters</h4>
                    <p>Learn how learning rate, discount factor, and exploration parameters affect agent performance and convergence speed.</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Built with modern web technologies for educational purposes</p>
            <div class="tech-stack">
                <span class="tech-item">HTML5</span>
                <span class="tech-item">CSS3</span>
                <span class="tech-item">JavaScript ES6+</span>
                <span class="tech-item">Chart.js</span>
                <span class="tech-item">Responsive Design</span>
            </div>
        </div>
    </div>
</body>
</html>
