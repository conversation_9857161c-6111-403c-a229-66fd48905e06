<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Build Google Clone - Step-by-Step Tutorial for CS Students</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .progress-bar {
            background: #ecf0f1;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 80vh;
        }

        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
        }

        .step-nav {
            list-style: none;
        }

        .step-nav li {
            margin-bottom: 15px;
        }

        .step-nav a {
            display: block;
            padding: 15px 20px;
            text-decoration: none;
            color: #5a6c7d;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            border-left: 4px solid transparent;
        }

        .step-nav a:hover {
            background: #e9ecef;
            color: #2c3e50;
        }

        .step-nav a.active {
            background: #3498db;
            color: white;
            border-left-color: #2980b9;
        }

        .step-nav a.completed {
            background: #27ae60;
            color: white;
            border-left-color: #229954;
        }

        .step-nav .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            margin-right: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .content-area {
            padding: 40px;
            overflow-y: auto;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .step-header {
            margin-bottom: 30px;
        }

        .step-header h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .step-header .difficulty {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .difficulty.beginner {
            background: #d4edda;
            color: #155724;
        }

        .difficulty.intermediate {
            background: #fff3cd;
            color: #856404;
        }

        .difficulty.advanced {
            background: #f8d7da;
            color: #721c24;
        }

        .step-header .time {
            display: inline-block;
            margin-left: 15px;
            color: #6c757d;
            font-size: 14px;
        }

        .objective {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .objective h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .objective p {
            color: #5a6c7d;
            line-height: 1.6;
        }

        .code-editor {
            background: #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .code-header {
            background: #34495e;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .code-header .file-name {
            color: #ecf0f1;
            font-weight: 600;
        }

        .code-header .dots {
            margin-left: auto;
            display: flex;
            gap: 5px;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot.red { background: #e74c3c; }
        .dot.yellow { background: #f1c40f; }
        .dot.green { background: #27ae60; }

        .code-content {
            padding: 20px;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
        }

        .code-content .tag { color: #e74c3c; }
        .code-content .attribute { color: #f39c12; }
        .code-content .value { color: #27ae60; }
        .code-content .comment { color: #95a5a6; font-style: italic; }
        .code-content .property { color: #3498db; }

        .preview-frame {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
            margin: 20px 0;
            overflow: hidden;
        }

        .preview-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }

        .preview-content {
            padding: 20px;
            min-height: 200px;
        }

        .instructions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }

        .instructions h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .instructions ol {
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 10px;
            color: #5a6c7d;
            line-height: 1.6;
        }

        .instructions code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }

        .tip {
            background: #d4edda;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .tip strong {
            color: #155724;
        }

        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .warning strong {
            color: #856404;
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .checklist {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .checklist h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .checklist-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .checklist-item:hover {
            background: #f8f9fa;
        }

        .checklist-item input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .checklist-item label {
            color: #5a6c7d;
            cursor: pointer;
            flex: 1;
        }

        .checklist-item.completed label {
            text-decoration: line-through;
            color: #27ae60;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .sidebar,
            .content-area {
                padding: 20px;
            }
            
            .step-nav a {
                padding: 12px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Build Google Clone</h1>
            <p>Step-by-step tutorial for CS students to create a Google-like search interface</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <nav class="step-nav">
                    <li><a href="#step1" class="step-link active" data-step="1">
                        <span class="step-number">1</span>Basic HTML Structure
                    </a></li>
                    <li><a href="#step2" class="step-link" data-step="2">
                        <span class="step-number">2</span>Google Logo & Styling
                    </a></li>
                    <li><a href="#step3" class="step-link" data-step="3">
                        <span class="step-number">3</span>Search Bar Design
                    </a></li>
                    <li><a href="#step4" class="step-link" data-step="4">
                        <span class="step-number">4</span>Search Buttons
                    </a></li>
                    <li><a href="#step5" class="step-link" data-step="5">
                        <span class="step-number">5</span>Navigation Header
                    </a></li>
                    <li><a href="#step6" class="step-link" data-step="6">
                        <span class="step-number">6</span>Footer Links
                    </a></li>
                    <li><a href="#step7" class="step-link" data-step="7">
                        <span class="step-number">7</span>Responsive Design
                    </a></li>
                    <li><a href="#step8" class="step-link" data-step="8">
                        <span class="step-number">8</span>Search Functionality
                    </a></li>
                    <li><a href="#step9" class="step-link" data-step="9">
                        <span class="step-number">9</span>Advanced Features
                    </a></li>
                    <li><a href="#step10" class="step-link" data-step="10">
                        <span class="step-number">10</span>Final Polish
                    </a></li>
                </nav>
            </div>

            <div class="content-area">
                <!-- Step 1: Basic HTML Structure -->
                <div class="step-content active" id="step1">
                    <div class="step-header">
                        <h2>Step 1: Basic HTML Structure</h2>
                        <span class="difficulty beginner">Beginner</span>
                        <span class="time">⏱️ 15 minutes</span>
                    </div>

                    <div class="objective">
                        <h3>🎯 Learning Objective</h3>
                        <p>Create the foundational HTML structure for our Google clone, understanding semantic HTML elements and proper document structure.</p>
                    </div>

                    <div class="instructions">
                        <h4>📝 Instructions</h4>
                        <ol>
                            <li>Create a new HTML file called <code>google-clone.html</code></li>
                            <li>Set up the basic HTML5 document structure</li>
                            <li>Add the viewport meta tag for responsive design</li>
                            <li>Create the main container elements</li>
                            <li>Add semantic HTML elements for better structure</li>
                        </ol>
                    </div>

                    <div class="code-editor">
                        <div class="code-header">
                            <span class="file-name">google-clone.html</span>
                            <div class="dots">
                                <div class="dot red"></div>
                                <div class="dot yellow"></div>
                                <div class="dot green"></div>
                            </div>
                        </div>
                        <div class="code-content">
<span class="comment">&lt;!-- Step 1: Basic HTML Structure --&gt;</span>
<span class="tag">&lt;!DOCTYPE html&gt;</span>
<span class="tag">&lt;html</span> <span class="attribute">lang</span>=<span class="value">"en"</span><span class="tag">&gt;</span>
<span class="tag">&lt;head&gt;</span>
    <span class="tag">&lt;meta</span> <span class="attribute">charset</span>=<span class="value">"UTF-8"</span><span class="tag">&gt;</span>
    <span class="tag">&lt;meta</span> <span class="attribute">name</span>=<span class="value">"viewport"</span> <span class="attribute">content</span>=<span class="value">"width=device-width, initial-scale=1.0"</span><span class="tag">&gt;</span>
    <span class="tag">&lt;title&gt;</span>Google Clone<span class="tag">&lt;/title&gt;</span>
    <span class="tag">&lt;style&gt;</span>
        <span class="comment">/* Basic reset and body styling */</span>
        <span class="property">* {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #fff;
            color: #3c4043;
        }</span>
    <span class="tag">&lt;/style&gt;</span>
<span class="tag">&lt;/head&gt;</span>
<span class="tag">&lt;body&gt;</span>
    <span class="comment">&lt;!-- Header Navigation --&gt;</span>
    <span class="tag">&lt;header</span> <span class="attribute">class</span>=<span class="value">"header"</span><span class="tag">&gt;</span>
        <span class="tag">&lt;nav</span> <span class="attribute">class</span>=<span class="value">"nav"</span><span class="tag">&gt;</span>
            <span class="comment">&lt;!-- Navigation content will go here --&gt;</span>
        <span class="tag">&lt;/nav&gt;</span>
    <span class="tag">&lt;/header&gt;</span>

    <span class="comment">&lt;!-- Main Content --&gt;</span>
    <span class="tag">&lt;main</span> <span class="attribute">class</span>=<span class="value">"main"</span><span class="tag">&gt;</span>
        <span class="comment">&lt;!-- Google logo and search will go here --&gt;</span>
    <span class="tag">&lt;/main&gt;</span>

    <span class="comment">&lt;!-- Footer --&gt;</span>
    <span class="tag">&lt;footer</span> <span class="attribute">class</span>=<span class="value">"footer"</span><span class="tag">&gt;</span>
        <span class="comment">&lt;!-- Footer links will go here --&gt;</span>
    <span class="tag">&lt;/footer&gt;</span>
<span class="tag">&lt;/body&gt;</span>
<span class="tag">&lt;/html&gt;</span>
                        </div>
                    </div>

                    <div class="preview-frame">
                        <div class="preview-header">Preview - Step 1</div>
                        <div class="preview-content">
                            <p style="color: #666; text-align: center; padding: 50px;">
                                Basic HTML structure created! You should see a blank page with proper document structure.
                                Open the file in your browser to verify it loads correctly.
                            </p>
                        </div>
                    </div>

                    <div class="checklist">
                        <h4>✅ Completion Checklist</h4>
                        <div class="checklist-item">
                            <input type="checkbox" id="check1-1">
                            <label for="check1-1">HTML5 doctype declaration added</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check1-2">
                            <label for="check1-2">Viewport meta tag included for responsive design</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check1-3">
                            <label for="check1-3">Semantic HTML elements (header, main, footer) created</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check1-4">
                            <label for="check1-4">Basic CSS reset applied</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check1-5">
                            <label for="check1-5">File opens successfully in browser</label>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 Pro Tip:</strong> Always use semantic HTML elements like &lt;header&gt;, &lt;main&gt;, and &lt;footer&gt; instead of generic &lt;div&gt; elements. This improves accessibility and SEO while making your code more readable.
                    </div>

                    <div class="navigation-buttons">
                        <button class="btn btn-secondary" disabled>← Previous</button>
                        <button class="btn btn-primary" onclick="nextStep()">Next Step →</button>
                    </div>
                </div>

                <!-- Step 2: Google Logo & Styling -->
                <div class="step-content" id="step2">
                    <div class="step-header">
                        <h2>Step 2: Google Logo & Styling</h2>
                        <span class="difficulty beginner">Beginner</span>
                        <span class="time">⏱️ 20 minutes</span>
                    </div>

                    <div class="objective">
                        <h3>🎯 Learning Objective</h3>
                        <p>Add the Google logo and implement the centered layout that makes Google's homepage distinctive. Learn CSS Flexbox for perfect centering.</p>
                    </div>

                    <div class="instructions">
                        <h4>📝 Instructions</h4>
                        <ol>
                            <li>Add the Google logo to the main section</li>
                            <li>Create CSS styles for centering content</li>
                            <li>Implement the classic Google homepage layout</li>
                            <li>Add proper spacing and typography</li>
                            <li>Test the responsive behavior</li>
                        </ol>
                    </div>

                    <div class="code-editor">
                        <div class="code-header">
                            <span class="file-name">google-clone.html (Updated Main Section)</span>
                            <div class="dots">
                                <div class="dot red"></div>
                                <div class="dot yellow"></div>
                                <div class="dot green"></div>
                            </div>
                        </div>
                        <div class="code-content">
<span class="comment">&lt;!-- Update the main section --&gt;</span>
<span class="tag">&lt;main</span> <span class="attribute">class</span>=<span class="value">"main"</span><span class="tag">&gt;</span>
    <span class="tag">&lt;div</span> <span class="attribute">class</span>=<span class="value">"logo-container"</span><span class="tag">&gt;</span>
        <span class="tag">&lt;img</span> <span class="attribute">src</span>=<span class="value">"https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png"</span>
             <span class="attribute">alt</span>=<span class="value">"Google"</span> <span class="attribute">class</span>=<span class="value">"google-logo"</span><span class="tag">&gt;</span>
    <span class="tag">&lt;/div&gt;</span>
<span class="tag">&lt;/main&gt;</span>

<span class="comment">&lt;!-- Add these styles to your &lt;style&gt; section --&gt;</span>
<span class="tag">&lt;style&gt;</span>
<span class="property">/* Main content styling */
.main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 120px);
    padding: 20px;
}

.logo-container {
    margin-bottom: 30px;
}

.google-logo {
    max-width: 272px;
    height: auto;
    display: block;
}

/* Responsive logo sizing */
@media (max-width: 768px) {
    .google-logo {
        max-width: 200px;
    }
}</span>
<span class="tag">&lt;/style&gt;</span>
                        </div>
                    </div>

                    <div class="tip">
                        <strong>💡 Pro Tip:</strong> We're using Google's official logo URL for this tutorial. In a real project, you'd download and host the image locally for better performance and reliability.
                    </div>

                    <div class="checklist">
                        <h4>✅ Completion Checklist</h4>
                        <div class="checklist-item">
                            <input type="checkbox" id="check2-1">
                            <label for="check2-1">Google logo added and displays correctly</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check2-2">
                            <label for="check2-2">Logo is centered horizontally and vertically</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check2-3">
                            <label for="check2-3">Flexbox centering implemented</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="check2-4">
                            <label for="check2-4">Responsive logo sizing works on mobile</label>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Search Bar Design -->
                <div class="step-content" id="step3">
                    <div class="step-header">
                        <h2>Step 3: Search Bar Design</h2>
                        <span class="difficulty intermediate">Intermediate</span>
                        <span class="time">⏱️ 30 minutes</span>
                    </div>

                    <div class="objective">
                        <h3>🎯 Learning Objective</h3>
                        <p>Create Google's iconic search bar with proper styling, icons, and interactive states. Learn advanced CSS techniques for form styling.</p>
                    </div>

                    <div class="code-editor">
                        <div class="code-header">
                            <span class="file-name">Search Bar HTML & CSS</span>
                            <div class="dots">
                                <div class="dot red"></div>
                                <div class="dot yellow"></div>
                                <div class="dot green"></div>
                            </div>
                        </div>
                        <div class="code-content">
<span class="comment">&lt;!-- Add after logo-container in main --&gt;</span>
<span class="tag">&lt;div</span> <span class="attribute">class</span>=<span class="value">"search-container"</span><span class="tag">&gt;</span>
    <span class="tag">&lt;div</span> <span class="attribute">class</span>=<span class="value">"search-box"</span><span class="tag">&gt;</span>
        <span class="tag">&lt;svg</span> <span class="attribute">class</span>=<span class="value">"search-icon"</span> <span class="attribute">viewBox</span>=<span class="value">"0 0 24 24"</span><span class="tag">&gt;</span>
            <span class="tag">&lt;path</span> <span class="attribute">d</span>=<span class="value">"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"</span><span class="tag">/&gt;</span>
        <span class="tag">&lt;/svg&gt;</span>
        <span class="tag">&lt;input</span> <span class="attribute">type</span>=<span class="value">"text"</span> <span class="attribute">class</span>=<span class="value">"search-input"</span> <span class="attribute">placeholder</span>=<span class="value">""</span><span class="tag">&gt;</span>
        <span class="tag">&lt;svg</span> <span class="attribute">class</span>=<span class="value">"mic-icon"</span> <span class="attribute">viewBox</span>=<span class="value">"0 0 24 24"</span><span class="tag">&gt;</span>
            <span class="tag">&lt;path</span> <span class="attribute">d</span>=<span class="value">"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"</span><span class="tag">/&gt;</span>
        <span class="tag">&lt;/svg&gt;</span>
    <span class="tag">&lt;/div&gt;</span>
<span class="tag">&lt;/div&gt;</span>

<span class="comment">/* Search bar styles */</span>
<span class="property">.search-container {
    width: 100%;
    max-width: 584px;
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    align-items: center;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    padding: 10px 16px;
    background: #fff;
    transition: box-shadow 0.3s ease;
}

.search-box:hover {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
}

.search-box:focus-within {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
    border-color: transparent;
}

.search-icon, .mic-icon {
    width: 20px;
    height: 20px;
    fill: #9aa0a6;
    cursor: pointer;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    padding: 0 16px;
    color: #3c4043;
}

.mic-icon:hover {
    fill: #4285f4;
}</span>
                        </div>
                    </div>

                    <div class="warning">
                        <strong>⚠️ Important:</strong> The search input doesn't have a placeholder by default, just like Google. The search suggestions would appear as you type in a real implementation.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="google-clone-tutorial.js"></script>
</body>
</html>
