// Reinforcement Learning Demo - Q-Learning Implementation
class QLearningAgent {
    constructor(gridWidth, gridHeight, learningRate = 0.1, discountFactor = 0.9, epsilon = 1.0, epsilonDecay = 0.995) {
        this.gridWidth = gridWidth;
        this.gridHeight = gridHeight;
        this.learningRate = learningRate;
        this.discountFactor = discountFactor;
        this.epsilon = epsilon;
        this.epsilonDecay = epsilonDecay;
        this.minEpsilon = 0.01;
        
        // Actions: 0=up, 1=right, 2=down, 3=left
        this.actions = [
            { dx: 0, dy: -1, name: 'up' },
            { dx: 1, dy: 0, name: 'right' },
            { dx: 0, dy: 1, name: 'down' },
            { dx: -1, dy: 0, name: 'left' }
        ];
        
        // Initialize Q-table
        this.qTable = {};
        this.initializeQTable();
        
        // Statistics
        this.episode = 0;
        this.totalSteps = 0;
        this.totalReward = 0;
        this.successfulEpisodes = 0;
        this.episodeRewards = [];
    }
    
    initializeQTable() {
        for (let x = 0; x < this.gridWidth; x++) {
            for (let y = 0; y < this.gridHeight; y++) {
                const state = `${x},${y}`;
                this.qTable[state] = [0, 0, 0, 0]; // Q-values for each action
            }
        }
    }
    
    getStateKey(x, y) {
        return `${x},${y}`;
    }
    
    getQValue(x, y, action) {
        const state = this.getStateKey(x, y);
        return this.qTable[state] ? this.qTable[state][action] : 0;
    }
    
    setQValue(x, y, action, value) {
        const state = this.getStateKey(x, y);
        if (!this.qTable[state]) {
            this.qTable[state] = [0, 0, 0, 0];
        }
        this.qTable[state][action] = value;
    }
    
    getBestAction(x, y) {
        const state = this.getStateKey(x, y);
        const qValues = this.qTable[state] || [0, 0, 0, 0];
        return qValues.indexOf(Math.max(...qValues));
    }
    
    chooseAction(x, y) {
        if (Math.random() < this.epsilon) {
            // Exploration: random action
            return Math.floor(Math.random() * this.actions.length);
        } else {
            // Exploitation: best known action
            return this.getBestAction(x, y);
        }
    }
    
    updateQValue(x, y, action, reward, nextX, nextY, isTerminal) {
        const currentQ = this.getQValue(x, y, action);
        let maxNextQ = 0;
        
        if (!isTerminal) {
            const nextState = this.getStateKey(nextX, nextY);
            const nextQValues = this.qTable[nextState] || [0, 0, 0, 0];
            maxNextQ = Math.max(...nextQValues);
        }
        
        const newQ = currentQ + this.learningRate * (reward + this.discountFactor * maxNextQ - currentQ);
        this.setQValue(x, y, action, newQ);
    }
    
    decayEpsilon() {
        this.epsilon = Math.max(this.minEpsilon, this.epsilon * this.epsilonDecay);
    }
    
    updateParameters(learningRate, discountFactor, epsilon, epsilonDecay) {
        this.learningRate = learningRate;
        this.discountFactor = discountFactor;
        this.epsilon = epsilon;
        this.epsilonDecay = epsilonDecay;
    }
}

class GridWorld {
    constructor() {
        this.width = 8;
        this.height = 6;
        this.agent = null;
        this.isTraining = false;
        this.isPaused = false;
        this.animationSpeed = 50;
        
        // Grid layout: 0=empty, 1=wall, 2=start, 3=goal
        this.grid = [
            [2, 0, 0, 1, 0, 0, 0, 0],
            [0, 1, 0, 1, 0, 1, 1, 0],
            [0, 1, 0, 0, 0, 0, 1, 0],
            [0, 0, 0, 1, 1, 0, 0, 0],
            [1, 0, 1, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 1, 0, 0, 3]
        ];
        
        this.startPos = { x: 0, y: 0 };
        this.goalPos = { x: 7, y: 5 };
        this.agentPos = { ...this.startPos };
        this.visitedCells = new Set();
        
        this.agent = new QLearningAgent(this.width, this.height);
        this.initializeUI();
        this.renderGrid();
        this.updateStats();
    }
    
    initializeUI() {
        // Button event listeners
        document.getElementById('startBtn').addEventListener('click', () => this.startTraining());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseTraining());
        document.getElementById('resetBtn').addEventListener('click', () => this.reset());
        document.getElementById('stepBtn').addEventListener('click', () => this.singleStep());
        
        // Speed slider
        document.getElementById('speedSlider').addEventListener('input', (e) => {
            this.animationSpeed = parseInt(e.target.value);
        });
        
        // Parameter inputs
        const paramInputs = ['learningRate', 'discountFactor', 'epsilon', 'epsilonDecay'];
        paramInputs.forEach(param => {
            document.getElementById(param).addEventListener('change', () => this.updateParameters());
        });
    }
    
    updateParameters() {
        const learningRate = parseFloat(document.getElementById('learningRate').value);
        const discountFactor = parseFloat(document.getElementById('discountFactor').value);
        const epsilon = parseFloat(document.getElementById('epsilon').value);
        const epsilonDecay = parseFloat(document.getElementById('epsilonDecay').value);
        
        this.agent.updateParameters(learningRate, discountFactor, epsilon, epsilonDecay);
    }
    
    renderGrid() {
        const gridElement = document.getElementById('grid');
        gridElement.innerHTML = '';
        
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.x = x;
                cell.dataset.y = y;
                
                // Set cell type
                const cellType = this.grid[y][x];
                if (cellType === 1) {
                    cell.classList.add('wall');
                    cell.textContent = '🧱';
                } else if (cellType === 2) {
                    cell.classList.add('start');
                    cell.textContent = '🏁';
                } else if (cellType === 3) {
                    cell.classList.add('goal');
                    cell.textContent = '🎯';
                } else {
                    cell.classList.add('empty');
                }
                
                // Show agent position
                if (x === this.agentPos.x && y === this.agentPos.y) {
                    cell.classList.add('agent');
                    cell.textContent = '🤖';
                }
                
                // Show visited cells
                if (this.visitedCells.has(`${x},${y}`) && !(x === this.agentPos.x && y === this.agentPos.y)) {
                    cell.classList.add('visited');
                }
                
                // Add Q-values display
                this.addQValuesDisplay(cell, x, y);
                
                gridElement.appendChild(cell);
            }
        }
    }
    
    addQValuesDisplay(cell, x, y) {
        const qValues = this.agent.qTable[`${x},${y}`] || [0, 0, 0, 0];
        const maxQ = Math.max(...qValues.map(Math.abs));
        
        if (maxQ > 0.01) {
            const directions = ['up', 'right', 'down', 'left'];
            directions.forEach((dir, index) => {
                const qSpan = document.createElement('span');
                qSpan.className = `q-value q-${dir}`;
                qSpan.textContent = qValues[index].toFixed(1);
                qSpan.style.color = qValues[index] > 0 ? '#27ae60' : '#e74c3c';
                qSpan.style.opacity = Math.abs(qValues[index]) / maxQ;
                cell.appendChild(qSpan);
            });
        }
    }
    
    isValidPosition(x, y) {
        return x >= 0 && x < this.width && y >= 0 && y < this.height && this.grid[y][x] !== 1;
    }
    
    getReward(x, y) {
        if (x === this.goalPos.x && y === this.goalPos.y) {
            return 100; // Goal reward
        } else if (this.grid[y][x] === 1) {
            return -10; // Wall penalty
        } else {
            return -1; // Step penalty
        }
    }
    
    isTerminal(x, y) {
        return x === this.goalPos.x && y === this.goalPos.y;
    }
    
    step() {
        const action = this.agent.chooseAction(this.agentPos.x, this.agentPos.y);
        const actionData = this.agent.actions[action];
        
        const newX = this.agentPos.x + actionData.dx;
        const newY = this.agentPos.y + actionData.dy;
        
        let reward;
        let nextX = this.agentPos.x;
        let nextY = this.agentPos.y;
        
        if (this.isValidPosition(newX, newY)) {
            nextX = newX;
            nextY = newY;
            reward = this.getReward(nextX, nextY);
        } else {
            reward = -10; // Invalid move penalty
        }
        
        // Update Q-value
        const isTerminal = this.isTerminal(nextX, nextY);
        this.agent.updateQValue(this.agentPos.x, this.agentPos.y, action, reward, nextX, nextY, isTerminal);
        
        // Move agent
        this.visitedCells.add(`${this.agentPos.x},${this.agentPos.y}`);
        this.agentPos.x = nextX;
        this.agentPos.y = nextY;
        
        // Update statistics
        this.agent.totalSteps++;
        this.agent.totalReward += reward;
        
        return { reward, isTerminal };
    }
    
    runEpisode() {
        return new Promise((resolve) => {
            const episodeSteps = [];
            let episodeReward = 0;
            let steps = 0;
            const maxSteps = 200;
            
            const stepInterval = setInterval(() => {
                if (this.isPaused || !this.isTraining) {
                    clearInterval(stepInterval);
                    resolve({ episodeReward, steps, success: false });
                    return;
                }
                
                const result = this.step();
                episodeReward += result.reward;
                steps++;
                
                this.renderGrid();
                this.updateStats();
                
                if (result.isTerminal) {
                    this.agent.successfulEpisodes++;
                    clearInterval(stepInterval);
                    resolve({ episodeReward, steps, success: true });
                } else if (steps >= maxSteps) {
                    clearInterval(stepInterval);
                    resolve({ episodeReward, steps, success: false });
                }
            }, 101 - this.animationSpeed);
        });
    }
    
    async startTraining() {
        if (this.isTraining) return;
        
        this.isTraining = true;
        this.isPaused = false;
        
        document.getElementById('startBtn').textContent = '⏸️ Running...';
        document.getElementById('startBtn').disabled = true;
        
        while (this.isTraining && !this.isPaused) {
            this.resetEpisode();
            const result = await this.runEpisode();
            
            this.agent.episode++;
            this.agent.episodeRewards.push(result.episodeReward);
            this.agent.decayEpsilon();
            
            // Brief pause between episodes
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        this.isTraining = false;
        document.getElementById('startBtn').textContent = '▶️ Start Training';
        document.getElementById('startBtn').disabled = false;
    }
    
    pauseTraining() {
        this.isPaused = !this.isPaused;
        if (this.isPaused) {
            document.getElementById('pauseBtn').textContent = '▶️ Resume';
        } else {
            document.getElementById('pauseBtn').textContent = '⏸️ Pause';
            if (!this.isTraining) {
                this.startTraining();
            }
        }
    }
    
    singleStep() {
        if (this.isTraining) return;
        
        const result = this.step();
        this.renderGrid();
        this.updateStats();
        
        if (result.isTerminal) {
            this.agent.episode++;
            this.agent.successfulEpisodes++;
            this.agent.decayEpsilon();
            setTimeout(() => this.resetEpisode(), 1000);
        }
    }
    
    resetEpisode() {
        this.agentPos = { ...this.startPos };
        this.visitedCells.clear();
    }
    
    reset() {
        this.isTraining = false;
        this.isPaused = false;
        this.agent = new QLearningAgent(this.width, this.height);
        this.resetEpisode();
        this.renderGrid();
        this.updateStats();
        
        document.getElementById('startBtn').textContent = '▶️ Start Training';
        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').textContent = '⏸️ Pause';
    }
    
    updateStats() {
        document.getElementById('episode').textContent = this.agent.episode;
        document.getElementById('steps').textContent = this.agent.totalSteps;
        document.getElementById('totalReward').textContent = this.agent.totalReward.toFixed(0);
        
        const successRate = this.agent.episode > 0 ? 
            (this.agent.successfulEpisodes / this.agent.episode * 100).toFixed(1) : 0;
        document.getElementById('successRate').textContent = `${successRate}%`;
        
        document.getElementById('epsilonValue').textContent = this.agent.epsilon.toFixed(3);
        
        // Update progress bar based on success rate
        document.getElementById('progressFill').style.width = `${successRate}%`;
    }
}

// Initialize the demo when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const gridWorld = new GridWorld();
    
    // Add some helpful tooltips and information
    console.log('🤖 Reinforcement Learning Demo Loaded!');
    console.log('📚 Q-Learning Algorithm Parameters:');
    console.log('   α (Learning Rate): How much new information overrides old information');
    console.log('   γ (Discount Factor): Importance of future rewards vs immediate rewards');
    console.log('   ε (Epsilon): Exploration vs exploitation trade-off');
    console.log('   Epsilon Decay: How quickly exploration decreases over time');
    console.log('🎯 Goal: Train the agent to find the optimal path from start to goal!');
});
