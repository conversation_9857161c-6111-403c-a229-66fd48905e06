<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Reinforcement Learning Demo - Multi-Algorithm Comparison</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .algorithm-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .algorithm-btn {
            padding: 10px 20px;
            border: 2px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .algorithm-btn.active {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
            transform: scale(1.05);
        }

        .algorithm-btn:hover {
            background: rgba(255,255,255,0.15);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr 400px;
            gap: 30px;
            padding: 30px;
        }

        .demo-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .grid-container {
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(10, 50px);
            grid-template-rows: repeat(8, 50px);
            gap: 2px;
            border: 3px solid #2c3e50;
            border-radius: 10px;
            padding: 10px;
            background: #2c3e50;
        }

        .cell {
            width: 50px;
            height: 50px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 10px;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .cell.empty { background: #ecf0f1; color: #7f8c8d; }
        .cell.wall { background: #34495e; color: white; }
        .cell.start { background: #27ae60; color: white; }
        .cell.goal { background: #e74c3c; color: white; }
        .cell.agent { background: #3498db; color: white; animation: pulse 1s infinite; }
        .cell.visited { background: #f39c12; opacity: 0.7; }
        .cell.path { background: #9b59b6; color: white; }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .charts-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .chart-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            height: 300px;
        }

        .chart-container h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            text-align: center;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .info-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .info-panel h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .stat {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .stat:last-child { border-bottom: none; }

        .stat-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .stat-value {
            color: #3498db;
            font-weight: bold;
        }

        .algorithm-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
        }

        .algorithm-info h4 {
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .algorithm-info p {
            color: #5a6c7d;
            font-size: 14px;
            line-height: 1.4;
        }

        .parameters {
            display: grid;
            gap: 15px;
        }

        .param-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .param-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .param-group input {
            padding: 8px 12px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .param-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .environment-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .env-btn {
            padding: 8px 16px;
            border: 2px solid #ecf0f1;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .env-btn.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }

        .progress-container {
            margin-top: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }

        @media (max-width: 1400px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: repeat(10, 35px);
                grid-template-rows: repeat(8, 35px);
            }
            
            .cell {
                width: 35px;
                height: 35px;
                font-size: 8px;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Advanced Reinforcement Learning Demo</h1>
            <p>Compare Multiple RL Algorithms: Q-Learning, SARSA, Deep Q-Network (DQN)</p>
            
            <div class="algorithm-selector">
                <div class="algorithm-btn active" data-algorithm="qlearning">Q-Learning</div>
                <div class="algorithm-btn" data-algorithm="sarsa">SARSA</div>
                <div class="algorithm-btn" data-algorithm="dqn">Deep Q-Network</div>
                <div class="algorithm-btn" data-algorithm="compare">Compare All</div>
            </div>
        </div>

        <div class="main-content">
            <div class="demo-area">
                <div class="controls">
                    <button class="btn btn-primary" id="startBtn">▶️ Start Training</button>
                    <button class="btn btn-warning" id="pauseBtn">⏸️ Pause</button>
                    <button class="btn btn-danger" id="resetBtn">🔄 Reset</button>
                    <button class="btn btn-success" id="stepBtn">👣 Single Step</button>
                    <label>
                        Speed: <input type="range" id="speedSlider" min="1" max="100" value="50" style="margin-left: 10px;">
                    </label>
                </div>

                <div class="environment-selector">
                    <div class="env-btn active" data-env="maze">🏰 Maze</div>
                    <div class="env-btn" data-env="cliff">🏔️ Cliff Walking</div>
                    <div class="env-btn" data-env="windy">💨 Windy Grid</div>
                    <div class="env-btn" data-env="custom">✏️ Custom</div>
                </div>

                <div class="grid-container">
                    <div class="grid" id="grid"></div>
                </div>
            </div>

            <div class="charts-area">
                <div class="chart-container">
                    <h3>📈 Learning Progress</h3>
                    <canvas id="rewardChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>🎯 Success Rate</h3>
                    <canvas id="successChart"></canvas>
                </div>
            </div>

            <div class="sidebar">
                <div class="info-panel">
                    <h3>📊 Training Statistics</h3>
                    <div class="stat">
                        <span class="stat-label">Algorithm:</span>
                        <span class="stat-value" id="currentAlgorithm">Q-Learning</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Episode:</span>
                        <span class="stat-value" id="episode">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Steps:</span>
                        <span class="stat-value" id="steps">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Total Reward:</span>
                        <span class="stat-value" id="totalReward">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Success Rate:</span>
                        <span class="stat-value" id="successRate">0%</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Avg Steps:</span>
                        <span class="stat-value" id="avgSteps">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Exploration:</span>
                        <span class="stat-value" id="epsilonValue">1.0</span>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <div class="info-panel">
                    <h3>⚙️ Hyperparameters</h3>
                    <div class="parameters">
                        <div class="param-group">
                            <label>Learning Rate (α):</label>
                            <input type="number" id="learningRate" value="0.1" min="0" max="1" step="0.01">
                        </div>
                        <div class="param-group">
                            <label>Discount Factor (γ):</label>
                            <input type="number" id="discountFactor" value="0.9" min="0" max="1" step="0.01">
                        </div>
                        <div class="param-group">
                            <label>Epsilon (ε):</label>
                            <input type="number" id="epsilon" value="1.0" min="0" max="1" step="0.01">
                        </div>
                        <div class="param-group">
                            <label>Epsilon Decay:</label>
                            <input type="number" id="epsilonDecay" value="0.995" min="0.9" max="1" step="0.001">
                        </div>
                    </div>
                </div>

                <div class="info-panel">
                    <h3>🤖 Algorithm Info</h3>
                    <div id="algorithmInfo" class="algorithm-info">
                        <h4>Q-Learning</h4>
                        <p>Off-policy temporal difference learning algorithm that learns the optimal action-value function Q*(s,a) regardless of the policy being followed.</p>
                    </div>
                </div>

                <div class="info-panel">
                    <h3>📋 Performance Comparison</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Algorithm</th>
                                <th>Episodes</th>
                                <th>Success %</th>
                                <th>Avg Steps</th>
                            </tr>
                        </thead>
                        <tbody id="comparisonTable">
                            <tr>
                                <td>Q-Learning</td>
                                <td>0</td>
                                <td>0%</td>
                                <td>0</td>
                            </tr>
                            <tr>
                                <td>SARSA</td>
                                <td>0</td>
                                <td>0%</td>
                                <td>0</td>
                            </tr>
                            <tr>
                                <td>DQN</td>
                                <td>0</td>
                                <td>0%</td>
                                <td>0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="advanced-rl-demo.js"></script>
</body>
</html>
