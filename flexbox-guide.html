<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Flexbox Guide for CS Students</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav {
            background: #34495e;
            padding: 15px 0;
            text-align: center;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .nav a:hover {
            background: #3498db;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin: 25px 0 15px 0;
        }

        .property-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .property-card {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            border-radius: 0 8px 8px 0;
        }

        .property-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .property-card .syntax {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }

        .property-card .values {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .property-card .values strong {
            color: #2c3e50;
        }

        .example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .example h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .flex-demo {
            display: flex;
            background: #ecf0f1;
            border: 2px dashed #3498db;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
        }

        .flex-item-demo {
            background: #e74c3c;
            color: white;
            padding: 15px;
            margin: 5px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }

        .flex-item-demo:nth-child(2) { background: #27ae60; }
        .flex-item-demo:nth-child(3) { background: #f39c12; }
        .flex-item-demo:nth-child(4) { background: #9b59b6; }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }

        .code-block .property { color: #3498db; }
        .code-block .value { color: #e74c3c; }
        .code-block .comment { color: #95a5a6; font-style: italic; }

        .tip {
            background: #d4edda;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .tip strong {
            color: #155724;
        }

        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .warning strong {
            color: #856404;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .property-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Complete Flexbox Guide</h1>
            <p>Comprehensive reference for CSS Flexbox layout system</p>
        </div>

        <div class="nav">
            <a href="#basics">Basics</a>
            <a href="#container">Container Properties</a>
            <a href="#items">Item Properties</a>
            <a href="#examples">Examples</a>
            <a href="#patterns">Common Patterns</a>
            <a href="flexbox-tutorial.html">🎯 Interactive Tutorial</a>
        </div>

        <div class="content">
            <section id="basics" class="section">
                <h2>🎯 Flexbox Basics</h2>
                
                <h3>What is Flexbox?</h3>
                <p>CSS Flexible Box Layout (Flexbox) is a one-dimensional layout method for arranging items in rows or columns. It provides an efficient way to distribute space and align items in a container, even when their size is unknown or dynamic.</p>

                <h3>Key Concepts</h3>
                <div class="property-grid">
                    <div class="property-card">
                        <h4>🏠 Flex Container (Parent)</h4>
                        <p>The element with <code>display: flex</code> or <code>display: inline-flex</code>. It establishes the flex formatting context.</p>
                    </div>
                    <div class="property-card">
                        <h4>📦 Flex Items (Children)</h4>
                        <p>Direct children of the flex container. They participate in the flex layout.</p>
                    </div>
                    <div class="property-card">
                        <h4>↔️ Main Axis</h4>
                        <p>The primary axis along which flex items are laid out. Defined by <code>flex-direction</code>.</p>
                    </div>
                    <div class="property-card">
                        <h4>↕️ Cross Axis</h4>
                        <p>The axis perpendicular to the main axis. Items can be aligned along this axis.</p>
                    </div>
                </div>

                <div class="example">
                    <h4>Basic Flexbox Setup</h4>
                    <div class="code-block">
<span class="comment">/* Make an element a flex container */</span>
<span class="property">.container</span> {
    <span class="property">display</span>: <span class="value">flex</span>;
}

<span class="comment">/* All direct children become flex items */</span>
<span class="property">.item</span> {
    <span class="comment">/* Flex items have special properties */</span>
    <span class="property">flex</span>: <span class="value">1</span>; <span class="comment">/* Shorthand for flex-grow, flex-shrink, flex-basis */</span>
}
                    </div>
                </div>
            </section>

            <section id="container" class="section">
                <h2>🏠 Flex Container Properties</h2>
                <p>These properties are applied to the parent element (flex container):</p>

                <div class="property-grid">
                    <div class="property-card">
                        <h4>flex-direction</h4>
                        <div class="syntax">flex-direction: row | row-reverse | column | column-reverse;</div>
                        <div class="values">
                            <strong>row</strong> (default): left to right<br>
                            <strong>row-reverse</strong>: right to left<br>
                            <strong>column</strong>: top to bottom<br>
                            <strong>column-reverse</strong>: bottom to top
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>flex-wrap</h4>
                        <div class="syntax">flex-wrap: nowrap | wrap | wrap-reverse;</div>
                        <div class="values">
                            <strong>nowrap</strong> (default): single line<br>
                            <strong>wrap</strong>: multi-line, top to bottom<br>
                            <strong>wrap-reverse</strong>: multi-line, bottom to top
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>justify-content</h4>
                        <div class="syntax">justify-content: flex-start | flex-end | center | space-between | space-around | space-evenly;</div>
                        <div class="values">
                            <strong>flex-start</strong> (default): items at start<br>
                            <strong>center</strong>: items centered<br>
                            <strong>space-between</strong>: equal space between items<br>
                            <strong>space-around</strong>: equal space around items<br>
                            <strong>space-evenly</strong>: equal space everywhere
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>align-items</h4>
                        <div class="syntax">align-items: stretch | flex-start | flex-end | center | baseline;</div>
                        <div class="values">
                            <strong>stretch</strong> (default): items stretch to fill<br>
                            <strong>flex-start</strong>: items at cross-start<br>
                            <strong>flex-end</strong>: items at cross-end<br>
                            <strong>center</strong>: items centered<br>
                            <strong>baseline</strong>: items aligned by baseline
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>align-content</h4>
                        <div class="syntax">align-content: stretch | flex-start | flex-end | center | space-between | space-around;</div>
                        <div class="values">
                            Only applies when <code>flex-wrap: wrap</code><br>
                            Aligns wrapped lines along cross axis<br>
                            Similar values to <code>justify-content</code>
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>gap</h4>
                        <div class="syntax">gap: &lt;length&gt; | &lt;percentage&gt;;</div>
                        <div class="values">
                            Sets space between items<br>
                            <strong>Example:</strong> <code>gap: 20px;</code><br>
                            Can also use <code>row-gap</code> and <code>column-gap</code>
                        </div>
                    </div>
                </div>
            </section>

            <section id="items" class="section">
                <h2>📦 Flex Item Properties</h2>
                <p>These properties are applied to the children (flex items):</p>

                <div class="property-grid">
                    <div class="property-card">
                        <h4>flex-grow</h4>
                        <div class="syntax">flex-grow: &lt;number&gt;; /* default: 0 */</div>
                        <div class="values">
                            Defines how much an item should grow<br>
                            <strong>0</strong>: don't grow<br>
                            <strong>1</strong>: grow equally with other items<br>
                            <strong>2</strong>: grow twice as much as items with flex-grow: 1
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>flex-shrink</h4>
                        <div class="syntax">flex-shrink: &lt;number&gt;; /* default: 1 */</div>
                        <div class="values">
                            Defines how much an item should shrink<br>
                            <strong>0</strong>: don't shrink<br>
                            <strong>1</strong>: shrink equally with other items<br>
                            Higher numbers shrink more
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>flex-basis</h4>
                        <div class="syntax">flex-basis: auto | &lt;length&gt; | &lt;percentage&gt;;</div>
                        <div class="values">
                            Defines initial size before free space is distributed<br>
                            <strong>auto</strong> (default): based on content<br>
                            <strong>0</strong>: ignore content size<br>
                            <strong>200px</strong>: specific size
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>flex (shorthand)</h4>
                        <div class="syntax">flex: &lt;grow&gt; &lt;shrink&gt; &lt;basis&gt;;</div>
                        <div class="values">
                            <strong>flex: 1;</strong> = flex: 1 1 0;<br>
                            <strong>flex: auto;</strong> = flex: 1 1 auto;<br>
                            <strong>flex: none;</strong> = flex: 0 0 auto;
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>align-self</h4>
                        <div class="syntax">align-self: auto | flex-start | flex-end | center | baseline | stretch;</div>
                        <div class="values">
                            Overrides <code>align-items</code> for individual item<br>
                            <strong>auto</strong> (default): inherit from parent<br>
                            Other values same as <code>align-items</code>
                        </div>
                    </div>

                    <div class="property-card">
                        <h4>order</h4>
                        <div class="syntax">order: &lt;integer&gt;; /* default: 0 */</div>
                        <div class="values">
                            Controls visual order without changing HTML<br>
                            <strong>0</strong> (default): natural order<br>
                            <strong>-1</strong>: appears first<br>
                            <strong>1</strong>: appears last
                        </div>
                    </div>
                </div>
            </section>

            <section id="examples" class="section">
                <h2>💡 Practical Examples</h2>

                <div class="example">
                    <h4>1. Perfect Centering</h4>
                    <div class="code-block">
<span class="property">.center-container</span> {
    <span class="property">display</span>: <span class="value">flex</span>;
    <span class="property">justify-content</span>: <span class="value">center</span>;
    <span class="property">align-items</span>: <span class="value">center</span>;
    <span class="property">height</span>: <span class="value">100vh</span>;
}
                    </div>
                    <div class="flex-demo" style="justify-content: center; align-items: center; height: 100px;">
                        <div class="flex-item-demo">Centered!</div>
                    </div>
                </div>

                <div class="example">
                    <h4>2. Equal Width Columns</h4>
                    <div class="code-block">
<span class="property">.equal-columns</span> {
    <span class="property">display</span>: <span class="value">flex</span>;
}

<span class="property">.column</span> {
    <span class="property">flex</span>: <span class="value">1</span>; <span class="comment">/* Each column takes equal space */</span>
}
                    </div>
                    <div class="flex-demo">
                        <div class="flex-item-demo" style="flex: 1;">Column 1</div>
                        <div class="flex-item-demo" style="flex: 1;">Column 2</div>
                        <div class="flex-item-demo" style="flex: 1;">Column 3</div>
                    </div>
                </div>

                <div class="example">
                    <h4>3. Sidebar Layout</h4>
                    <div class="code-block">
<span class="property">.layout</span> {
    <span class="property">display</span>: <span class="value">flex</span>;
}

<span class="property">.sidebar</span> {
    <span class="property">flex</span>: <span class="value">0 0 200px</span>; <span class="comment">/* Fixed width sidebar */</span>
}

<span class="property">.main-content</span> {
    <span class="property">flex</span>: <span class="value">1</span>; <span class="comment">/* Takes remaining space */</span>
}
                    </div>
                    <div class="flex-demo">
                        <div class="flex-item-demo" style="flex: 0 0 100px;">Sidebar</div>
                        <div class="flex-item-demo" style="flex: 1;">Main Content</div>
                    </div>
                </div>
            </section>

            <section id="patterns" class="section">
                <h2>🎨 Common Layout Patterns</h2>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Pattern</th>
                            <th>CSS Properties</th>
                            <th>Use Case</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Navigation Bar</strong></td>
                            <td><code>display: flex; justify-content: space-between;</code></td>
                            <td>Logo on left, menu items on right</td>
                        </tr>
                        <tr>
                            <td><strong>Card Grid</strong></td>
                            <td><code>display: flex; flex-wrap: wrap; gap: 20px;</code></td>
                            <td>Responsive card layouts</td>
                        </tr>
                        <tr>
                            <td><strong>Sticky Footer</strong></td>
                            <td><code>min-height: 100vh; flex-direction: column;</code></td>
                            <td>Footer always at bottom</td>
                        </tr>
                        <tr>
                            <td><strong>Media Object</strong></td>
                            <td><code>display: flex; align-items: flex-start;</code></td>
                            <td>Image + text content</td>
                        </tr>
                        <tr>
                            <td><strong>Holy Grail</strong></td>
                            <td><code>flex-direction: column; main: flex: 1;</code></td>
                            <td>Header, main content, footer</td>
                        </tr>
                    </tbody>
                </table>

                <div class="tip">
                    <strong>💡 Pro Tip:</strong> Use <code>flex: 1</code> to make an item take up all available space. This is perfect for creating responsive layouts where one element grows to fill the container.
                </div>

                <div class="warning">
                    <strong>⚠️ Common Gotcha:</strong> Remember that <code>justify-content</code> works along the main axis, while <code>align-items</code> works along the cross axis. The main axis changes based on <code>flex-direction</code>!
                </div>
            </section>
        </div>

        <div class="footer">
            <p>🎯 Ready to practice? Try the <a href="flexbox-tutorial.html" style="color: #3498db;">Interactive Flexbox Tutorial</a></p>
            <p>Built for CS students to master modern CSS layouts</p>
        </div>
    </div>
</body>
</html>
