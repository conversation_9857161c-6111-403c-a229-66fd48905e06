// Advanced Reinforcement Learning Demo - Multiple Algorithms

// Base Agent Class
class RLAgent {
    constructor(gridWidth, gridHeight, learningRate = 0.1, discountFactor = 0.9, epsilon = 1.0, epsilonDecay = 0.995) {
        this.gridWidth = gridWidth;
        this.gridHeight = gridHeight;
        this.learningRate = learningRate;
        this.discountFactor = discountFactor;
        this.epsilon = epsilon;
        this.epsilonDecay = epsilonDecay;
        this.minEpsilon = 0.01;
        
        // Actions: 0=up, 1=right, 2=down, 3=left
        this.actions = [
            { dx: 0, dy: -1, name: 'up' },
            { dx: 1, dy: 0, name: 'right' },
            { dx: 0, dy: 1, name: 'down' },
            { dx: -1, dy: 0, name: 'left' }
        ];
        
        // Statistics
        this.episode = 0;
        this.totalSteps = 0;
        this.totalReward = 0;
        this.successfulEpisodes = 0;
        this.episodeRewards = [];
        this.episodeSteps = [];
        this.successHistory = [];
    }
    
    getStateKey(x, y) {
        return `${x},${y}`;
    }
    
    chooseAction(x, y) {
        if (Math.random() < this.epsilon) {
            return Math.floor(Math.random() * this.actions.length);
        } else {
            return this.getBestAction(x, y);
        }
    }
    
    decayEpsilon() {
        this.epsilon = Math.max(this.minEpsilon, this.epsilon * this.epsilonDecay);
    }
    
    updateParameters(learningRate, discountFactor, epsilon, epsilonDecay) {
        this.learningRate = learningRate;
        this.discountFactor = discountFactor;
        this.epsilon = epsilon;
        this.epsilonDecay = epsilonDecay;
    }
    
    // Abstract methods to be implemented by subclasses
    getBestAction(x, y) { throw new Error('Must implement getBestAction'); }
    updateValue(x, y, action, reward, nextX, nextY, isTerminal) { throw new Error('Must implement updateValue'); }
    reset() { throw new Error('Must implement reset'); }
}

// Q-Learning Agent
class QLearningAgent extends RLAgent {
    constructor(gridWidth, gridHeight, learningRate, discountFactor, epsilon, epsilonDecay) {
        super(gridWidth, gridHeight, learningRate, discountFactor, epsilon, epsilonDecay);
        this.qTable = {};
        this.initializeQTable();
    }
    
    initializeQTable() {
        for (let x = 0; x < this.gridWidth; x++) {
            for (let y = 0; y < this.gridHeight; y++) {
                const state = `${x},${y}`;
                this.qTable[state] = [0, 0, 0, 0];
            }
        }
    }
    
    getQValue(x, y, action) {
        const state = this.getStateKey(x, y);
        return this.qTable[state] ? this.qTable[state][action] : 0;
    }
    
    setQValue(x, y, action, value) {
        const state = this.getStateKey(x, y);
        if (!this.qTable[state]) {
            this.qTable[state] = [0, 0, 0, 0];
        }
        this.qTable[state][action] = value;
    }
    
    getBestAction(x, y) {
        const state = this.getStateKey(x, y);
        const qValues = this.qTable[state] || [0, 0, 0, 0];
        return qValues.indexOf(Math.max(...qValues));
    }
    
    updateValue(x, y, action, reward, nextX, nextY, isTerminal) {
        const currentQ = this.getQValue(x, y, action);
        let maxNextQ = 0;
        
        if (!isTerminal) {
            const nextState = this.getStateKey(nextX, nextY);
            const nextQValues = this.qTable[nextState] || [0, 0, 0, 0];
            maxNextQ = Math.max(...nextQValues);
        }
        
        const newQ = currentQ + this.learningRate * (reward + this.discountFactor * maxNextQ - currentQ);
        this.setQValue(x, y, action, newQ);
    }
    
    reset() {
        this.initializeQTable();
        this.episode = 0;
        this.totalSteps = 0;
        this.totalReward = 0;
        this.successfulEpisodes = 0;
        this.episodeRewards = [];
        this.episodeSteps = [];
        this.successHistory = [];
    }
}

// SARSA Agent
class SARSAAgent extends RLAgent {
    constructor(gridWidth, gridHeight, learningRate, discountFactor, epsilon, epsilonDecay) {
        super(gridWidth, gridHeight, learningRate, discountFactor, epsilon, epsilonDecay);
        this.qTable = {};
        this.initializeQTable();
        this.lastAction = null;
    }
    
    initializeQTable() {
        for (let x = 0; x < this.gridWidth; x++) {
            for (let y = 0; y < this.gridHeight; y++) {
                const state = `${x},${y}`;
                this.qTable[state] = [0, 0, 0, 0];
            }
        }
    }
    
    getQValue(x, y, action) {
        const state = this.getStateKey(x, y);
        return this.qTable[state] ? this.qTable[state][action] : 0;
    }
    
    setQValue(x, y, action, value) {
        const state = this.getStateKey(x, y);
        if (!this.qTable[state]) {
            this.qTable[state] = [0, 0, 0, 0];
        }
        this.qTable[state][action] = value;
    }
    
    getBestAction(x, y) {
        const state = this.getStateKey(x, y);
        const qValues = this.qTable[state] || [0, 0, 0, 0];
        return qValues.indexOf(Math.max(...qValues));
    }
    
    updateValue(x, y, action, reward, nextX, nextY, isTerminal, nextAction = null) {
        const currentQ = this.getQValue(x, y, action);
        let nextQ = 0;
        
        if (!isTerminal && nextAction !== null) {
            nextQ = this.getQValue(nextX, nextY, nextAction);
        }
        
        const newQ = currentQ + this.learningRate * (reward + this.discountFactor * nextQ - currentQ);
        this.setQValue(x, y, action, newQ);
    }
    
    reset() {
        this.initializeQTable();
        this.episode = 0;
        this.totalSteps = 0;
        this.totalReward = 0;
        this.successfulEpisodes = 0;
        this.episodeRewards = [];
        this.episodeSteps = [];
        this.successHistory = [];
        this.lastAction = null;
    }
}

// Simple DQN Agent (Neural Network approximation)
class DQNAgent extends RLAgent {
    constructor(gridWidth, gridHeight, learningRate, discountFactor, epsilon, epsilonDecay) {
        super(gridWidth, gridHeight, learningRate, discountFactor, epsilon, epsilonDecay);
        this.networkWeights = this.initializeNetwork();
        this.targetWeights = JSON.parse(JSON.stringify(this.networkWeights));
        this.memory = [];
        this.memorySize = 1000;
        this.batchSize = 32;
        this.targetUpdateFreq = 100;
        this.updateCounter = 0;
    }
    
    initializeNetwork() {
        // Simple 2-layer network: input(2) -> hidden(16) -> output(4)
        return {
            w1: Array(2).fill().map(() => Array(16).fill().map(() => (Math.random() - 0.5) * 0.1)),
            b1: Array(16).fill().map(() => (Math.random() - 0.5) * 0.1),
            w2: Array(16).fill().map(() => Array(4).fill().map(() => (Math.random() - 0.5) * 0.1)),
            b2: Array(4).fill().map(() => (Math.random() - 0.5) * 0.1)
        };
    }
    
    forward(x, y, weights = null) {
        const w = weights || this.networkWeights;
        const input = [x / this.gridWidth, y / this.gridHeight]; // Normalize inputs
        
        // Hidden layer
        const hidden = w.w1[0].map((_, i) => {
            const sum = input.reduce((acc, inp, j) => acc + inp * w.w1[j][i], 0) + w.b1[i];
            return Math.max(0, sum); // ReLU activation
        });
        
        // Output layer
        const output = w.w2[0].map((_, i) => {
            const sum = hidden.reduce((acc, h, j) => acc + h * w.w2[j][i], 0) + w.b2[i];
            return sum;
        });
        
        return output;
    }
    
    getBestAction(x, y) {
        const qValues = this.forward(x, y);
        return qValues.indexOf(Math.max(...qValues));
    }
    
    updateValue(x, y, action, reward, nextX, nextY, isTerminal) {
        // Store experience in memory
        this.memory.push({ x, y, action, reward, nextX, nextY, isTerminal });
        if (this.memory.length > this.memorySize) {
            this.memory.shift();
        }
        
        // Train on batch if enough experiences
        if (this.memory.length >= this.batchSize) {
            this.trainBatch();
        }
        
        // Update target network periodically
        this.updateCounter++;
        if (this.updateCounter % this.targetUpdateFreq === 0) {
            this.targetWeights = JSON.parse(JSON.stringify(this.networkWeights));
        }
    }
    
    trainBatch() {
        // Simple batch training (simplified for demo)
        const batch = this.memory.slice(-this.batchSize);
        
        batch.forEach(exp => {
            const currentQ = this.forward(exp.x, exp.y);
            const target = [...currentQ];
            
            if (exp.isTerminal) {
                target[exp.action] = exp.reward;
            } else {
                const nextQ = this.forward(exp.nextX, exp.nextY, this.targetWeights);
                target[exp.action] = exp.reward + this.discountFactor * Math.max(...nextQ);
            }
            
            // Simple gradient update (very simplified)
            const error = target[exp.action] - currentQ[exp.action];
            this.networkWeights.b2[exp.action] += this.learningRate * error * 0.1;
        });
    }
    
    reset() {
        this.networkWeights = this.initializeNetwork();
        this.targetWeights = JSON.parse(JSON.stringify(this.networkWeights));
        this.memory = [];
        this.updateCounter = 0;
        this.episode = 0;
        this.totalSteps = 0;
        this.totalReward = 0;
        this.successfulEpisodes = 0;
        this.episodeRewards = [];
        this.episodeSteps = [];
        this.successHistory = [];
    }
}

// Environment Definitions
const ENVIRONMENTS = {
    maze: {
        width: 10,
        height: 8,
        grid: [
            [2, 0, 0, 1, 0, 0, 0, 1, 0, 0],
            [0, 1, 0, 1, 0, 1, 0, 1, 0, 1],
            [0, 1, 0, 0, 0, 1, 0, 0, 0, 1],
            [0, 0, 0, 1, 1, 1, 0, 1, 0, 0],
            [1, 0, 1, 0, 0, 0, 0, 1, 0, 1],
            [0, 0, 1, 0, 1, 0, 1, 0, 0, 0],
            [0, 1, 0, 0, 1, 0, 1, 0, 1, 0],
            [0, 0, 0, 1, 0, 0, 0, 0, 0, 3]
        ],
        start: { x: 0, y: 0 },
        goal: { x: 9, y: 7 }
    },
    cliff: {
        width: 10,
        height: 8,
        grid: [
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [2, 4, 4, 4, 4, 4, 4, 4, 4, 3]
        ],
        start: { x: 0, y: 7 },
        goal: { x: 9, y: 7 }
    },
    windy: {
        width: 10,
        height: 8,
        grid: [
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0],
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0],
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0],
            [2, 0, 0, 5, 5, 5, 5, 0, 0, 3],
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0],
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0],
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0],
            [0, 0, 0, 5, 5, 5, 5, 0, 0, 0]
        ],
        start: { x: 0, y: 3 },
        goal: { x: 9, y: 3 }
    }
};

// Advanced Grid World with Multiple Algorithms
class AdvancedGridWorld {
    constructor() {
        this.currentEnv = 'maze';
        this.currentAlgorithm = 'qlearning';
        this.agents = {};
        this.isTraining = false;
        this.isPaused = false;
        this.animationSpeed = 50;
        this.compareMode = false;

        this.loadEnvironment(this.currentEnv);
        this.initializeAgents();
        this.initializeUI();
        this.initializeCharts();
        this.renderGrid();
        this.updateStats();
    }

    loadEnvironment(envName) {
        const env = ENVIRONMENTS[envName];
        this.width = env.width;
        this.height = env.height;
        this.grid = env.grid.map(row => [...row]);
        this.startPos = { ...env.start };
        this.goalPos = { ...env.goal };
        this.agentPos = { ...this.startPos };
        this.visitedCells = new Set();
        this.currentEnv = envName;
    }

    initializeAgents() {
        this.agents = {
            qlearning: new QLearningAgent(this.width, this.height, 0.1, 0.9, 1.0, 0.995),
            sarsa: new SARSAAgent(this.width, this.height, 0.1, 0.9, 1.0, 0.995),
            dqn: new DQNAgent(this.width, this.height, 0.001, 0.9, 1.0, 0.995)
        };
    }

    getCurrentAgent() {
        return this.agents[this.currentAlgorithm];
    }
