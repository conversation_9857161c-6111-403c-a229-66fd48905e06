<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Flexbox Tutorial for CS Students</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            min-height: 80vh;
        }

        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
        }

        .demo-area {
            padding: 30px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .control-group select,
        .control-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn.secondary {
            background: #95a5a6;
        }

        .btn.secondary:hover {
            background: #7f8c8d;
        }

        .flex-container {
            background: #ecf0f1;
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            position: relative;
            transition: all 0.3s ease;
        }

        .flex-container::before {
            content: 'Flex Container';
            position: absolute;
            top: -15px;
            left: 20px;
            background: #3498db;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .flex-item {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            margin: 5px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            min-width: 80px;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .flex-item:nth-child(2) {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .flex-item:nth-child(3) {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .flex-item:nth-child(4) {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .flex-item:nth-child(5) {
            background: linear-gradient(135deg, #1abc9c, #16a085);
        }

        .flex-item:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .flex-item.selected {
            box-shadow: 0 0 0 3px #f1c40f;
            transform: scale(1.1);
        }

        .code-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            margin-top: 20px;
        }

        .code-display .property {
            color: #3498db;
        }

        .code-display .value {
            color: #e74c3c;
        }

        .code-display .comment {
            color: #95a5a6;
            font-style: italic;
        }

        .info-panel {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin-bottom: 20px;
        }

        .info-panel h4 {
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .info-panel p {
            color: #5a6c7d;
            font-size: 14px;
            line-height: 1.5;
        }

        .preset-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .preset-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #229954;
            transform: translateY(-1px);
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .tab {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab.active {
            color: #3498db;
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .axis-indicator {
            position: absolute;
            font-size: 12px;
            font-weight: bold;
            color: #3498db;
            background: rgba(52, 152, 219, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
        }

        .main-axis {
            top: 10px;
            right: 10px;
        }

        .cross-axis {
            bottom: 10px;
            right: 10px;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .sidebar,
            .demo-area {
                padding: 20px;
            }
            
            .flex-container {
                min-height: 200px;
                padding: 15px;
            }
            
            .flex-item {
                min-width: 60px;
                min-height: 60px;
                padding: 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Interactive Flexbox Tutorial</h1>
            <p>Master CSS Flexbox with hands-on examples and real-time visualization</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="tabs">
                    <button class="tab active" data-tab="container">Container</button>
                    <button class="tab" data-tab="items">Items</button>
                </div>

                <div class="tab-content active" id="container-tab">
                    <div class="info-panel">
                        <h4>Flex Container Properties</h4>
                        <p>These properties are applied to the parent element (flex container) to control the layout of its children.</p>
                    </div>

                    <div class="section">
                        <h3>🎛️ Quick Presets</h3>
                        <div class="preset-buttons">
                            <button class="preset-btn" data-preset="center">Center All</button>
                            <button class="preset-btn" data-preset="space-between">Space Between</button>
                            <button class="preset-btn" data-preset="column">Column Layout</button>
                            <button class="preset-btn" data-preset="wrap">Wrap Items</button>
                            <button class="preset-btn" data-preset="reverse">Reverse</button>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📐 Layout Direction</h3>
                        <div class="control-group">
                            <label for="flexDirection">flex-direction:</label>
                            <select id="flexDirection">
                                <option value="row">row (default)</option>
                                <option value="row-reverse">row-reverse</option>
                                <option value="column">column</option>
                                <option value="column-reverse">column-reverse</option>
                            </select>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📦 Wrapping</h3>
                        <div class="control-group">
                            <label for="flexWrap">flex-wrap:</label>
                            <select id="flexWrap">
                                <option value="nowrap">nowrap (default)</option>
                                <option value="wrap">wrap</option>
                                <option value="wrap-reverse">wrap-reverse</option>
                            </select>
                        </div>
                    </div>

                    <div class="section">
                        <h3>↔️ Main Axis Alignment</h3>
                        <div class="control-group">
                            <label for="justifyContent">justify-content:</label>
                            <select id="justifyContent">
                                <option value="flex-start">flex-start (default)</option>
                                <option value="flex-end">flex-end</option>
                                <option value="center">center</option>
                                <option value="space-between">space-between</option>
                                <option value="space-around">space-around</option>
                                <option value="space-evenly">space-evenly</option>
                            </select>
                        </div>
                    </div>

                    <div class="section">
                        <h3>↕️ Cross Axis Alignment</h3>
                        <div class="control-group">
                            <label for="alignItems">align-items:</label>
                            <select id="alignItems">
                                <option value="stretch">stretch (default)</option>
                                <option value="flex-start">flex-start</option>
                                <option value="flex-end">flex-end</option>
                                <option value="center">center</option>
                                <option value="baseline">baseline</option>
                            </select>
                        </div>
                    </div>

                    <div class="section">
                        <h3>📏 Multi-line Alignment</h3>
                        <div class="control-group">
                            <label for="alignContent">align-content:</label>
                            <select id="alignContent">
                                <option value="stretch">stretch (default)</option>
                                <option value="flex-start">flex-start</option>
                                <option value="flex-end">flex-end</option>
                                <option value="center">center</option>
                                <option value="space-between">space-between</option>
                                <option value="space-around">space-around</option>
                            </select>
                        </div>
                    </div>

                    <div class="section">
                        <h3>🔧 Container Controls</h3>
                        <div class="control-group">
                            <label for="containerHeight">Container Height (px):</label>
                            <input type="range" id="containerHeight" min="200" max="600" value="300">
                            <span id="heightValue">300px</span>
                        </div>
                        <div class="control-group">
                            <label for="gap">Gap (px):</label>
                            <input type="range" id="gap" min="0" max="30" value="5">
                            <span id="gapValue">5px</span>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="items-tab">
                    <div class="info-panel">
                        <h4>Flex Item Properties</h4>
                        <p>Click on any flex item to select it and modify its individual properties.</p>
                    </div>

                    <div class="section">
                        <h3>🎯 Selected Item: <span id="selectedItem">None</span></h3>
                        <div class="control-group">
                            <label for="flexGrow">flex-grow:</label>
                            <input type="number" id="flexGrow" min="0" max="5" value="0" step="1">
                        </div>
                        <div class="control-group">
                            <label for="flexShrink">flex-shrink:</label>
                            <input type="number" id="flexShrink" min="0" max="5" value="1" step="1">
                        </div>
                        <div class="control-group">
                            <label for="flexBasis">flex-basis:</label>
                            <select id="flexBasis">
                                <option value="auto">auto (default)</option>
                                <option value="0">0</option>
                                <option value="100px">100px</option>
                                <option value="200px">200px</option>
                                <option value="50%">50%</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="alignSelf">align-self:</label>
                            <select id="alignSelf">
                                <option value="auto">auto (default)</option>
                                <option value="flex-start">flex-start</option>
                                <option value="flex-end">flex-end</option>
                                <option value="center">center</option>
                                <option value="baseline">baseline</option>
                                <option value="stretch">stretch</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="order">order:</label>
                            <input type="number" id="order" min="-5" max="5" value="0" step="1">
                        </div>
                    </div>

                    <div class="section">
                        <h3>🔧 Item Controls</h3>
                        <button class="btn" onclick="addItem()">➕ Add Item</button>
                        <button class="btn secondary" onclick="removeItem()">➖ Remove Item</button>
                        <button class="btn secondary" onclick="resetItems()">🔄 Reset All</button>
                    </div>
                </div>
            </div>

            <div class="demo-area">
                <div class="flex-container" id="flexContainer">
                    <div class="axis-indicator main-axis" id="mainAxis">Main Axis →</div>
                    <div class="axis-indicator cross-axis" id="crossAxis">Cross Axis ↓</div>
                    <div class="flex-item" data-item="1">1</div>
                    <div class="flex-item" data-item="2">2</div>
                    <div class="flex-item" data-item="3">3</div>
                    <div class="flex-item" data-item="4">4</div>
                </div>

                <div class="code-display" id="codeDisplay">
                    <div class="comment">/* CSS Flexbox Code */</div>
                    <div><span class="property">.flex-container</span> {</div>
                    <div>&nbsp;&nbsp;<span class="property">display</span>: <span class="value">flex</span>;</div>
                    <div>&nbsp;&nbsp;<span class="property">flex-direction</span>: <span class="value">row</span>;</div>
                    <div>&nbsp;&nbsp;<span class="property">flex-wrap</span>: <span class="value">nowrap</span>;</div>
                    <div>&nbsp;&nbsp;<span class="property">justify-content</span>: <span class="value">flex-start</span>;</div>
                    <div>&nbsp;&nbsp;<span class="property">align-items</span>: <span class="value">stretch</span>;</div>
                    <div>&nbsp;&nbsp;<span class="property">align-content</span>: <span class="value">stretch</span>;</div>
                    <div>}</div>
                </div>
            </div>
        </div>
    </div>

    <script src="flexbox-tutorial.js"></script>
</body>
</html>
