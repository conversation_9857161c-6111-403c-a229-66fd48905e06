// Import components
import { createHeader } from './components/header.js';
import { createNavigation } from './components/navigation.js';
import { createHero, initCarousel } from './components/hero.js';
import { createProducts } from './components/products.js';
import { initCart } from './components/cart.js';
import { initSearch } from './components/search.js';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing JD Clone application...');
    
    try {
        // Render components
        document.getElementById('header').innerHTML = createHeader();
        document.getElementById('navigation').innerHTML = createNavigation();
        document.getElementById('hero').innerHTML = createHero();
        document.getElementById('products').innerHTML = createProducts();
        
        // Initialize functionality
        initCarousel();
        initCart();
        initSearch();
        
        console.log('JD Clone application initialized successfully!');
        
        // Add some demo interactions
        addDemoInteractions();
        
    } catch (error) {
        console.error('Error initializing application:', error);
    }
});

// Demo interactions for better user experience
function addDemoInteractions() {
    // Add loading states
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.textContent;
            this.textContent = 'Adding...';
            this.disabled = true;
            
            setTimeout(() => {
                this.textContent = 'Added!';
                this.style.background = '#28a745';
                
                setTimeout(() => {
                    this.textContent = originalText;
                    this.style.background = '';
                    this.disabled = false;
                }, 1500);
            }, 500);
        });
    });
    
    // Add product card hover effects
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add search input focus effects
    const searchInput = document.querySelector('.search-bar input');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    }
}

// Utility functions
export function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
}

export function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '★';
    }
    
    if (hasHalfStar) {
        stars += '☆';
    }
    
    return stars;
}

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

// Performance monitoring
window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
});
