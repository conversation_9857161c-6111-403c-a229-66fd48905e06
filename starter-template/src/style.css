/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header Styles */
.header-container {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-top {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 2rem;
}

.logo img {
    height: 40px;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #e50012;
}

.search-bar {
    flex: 1;
    display: flex;
    max-width: 500px;
}

.search-bar input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e50012;
    border-right: none;
    outline: none;
    border-radius: 4px 0 0 4px;
}

.search-btn {
    background: #e50012;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #c40010;
}

.user-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.login {
    text-decoration: none;
    color: #333;
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s;
}

.login:hover {
    background: #e50012;
    color: white;
    border-color: #e50012;
}

.cart {
    position: relative;
    text-decoration: none;
    color: #333;
    padding: 0.5rem;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e50012;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

/* Navigation Styles */
.nav-container {
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.nav-menu {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-menu li a {
    display: block;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #333;
    transition: background 0.3s;
}

.nav-menu li a:hover {
    background: #e50012;
    color: white;
}

/* Hero Section Styles */
.hero-carousel {
    position: relative;
    max-width: 1200px;
    margin: 2rem auto;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.carousel-container {
    position: relative;
    height: 400px;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e50012, #ff6b6b);
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-content {
    position: absolute;
    left: 2rem;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    z-index: 2;
}

.carousel-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.carousel-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.cta-button {
    background: white;
    color: #e50012;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.cta-button:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
}

.carousel-dots {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: background 0.3s;
}

.dot.active {
    background: white;
}

/* Products Section Styles */
.products-section {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.products-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #333;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.product-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 1rem;
}

.product-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.rating {
    color: #ffa500;
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #e50012;
    margin: 0.5rem 0;
}

.add-to-cart {
    width: 100%;
    background: #e50012;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 1rem;
}

.add-to-cart:hover {
    background: #c40010;
}

/* Product card enhancements */
.product-image {
    position: relative;
    overflow: hidden;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e50012;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.price-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.original-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0.5rem 0;
}

.stars {
    color: #ffa500;
}

.rating-value {
    font-weight: bold;
    color: #333;
}

.reviews {
    color: #666;
    font-size: 0.9rem;
}

/* Footer Styles */
.footer-container {
    background: #333;
    color: white;
    margin-top: 3rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem 1rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #e50012;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding: 1rem;
    text-align: center;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-top {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-bar {
        order: 3;
        max-width: 100%;
    }
    
    .nav-menu {
        flex-direction: column;
    }
    
    .nav-menu li a {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #ddd;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .carousel-content {
        padding: 1rem;
        left: 1rem;
    }
    
    .carousel-content h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .product-card img {
        height: 150px;
    }
    
    .header-top {
        padding: 0.5rem;
    }
    
    .carousel-content h2 {
        font-size: 1.2rem;
    }
    
    .carousel-content p {
        font-size: 1rem;
    }
}
