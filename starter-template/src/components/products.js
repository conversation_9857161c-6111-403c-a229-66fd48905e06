// Products component
export function createProducts() {
    const products = [
        { 
            id: 1, 
            name: 'iPhone 15 Pro', 
            price: 999, 
            originalPrice: 1199,
            image: 'https://via.placeholder.com/250x200/007bff/ffffff?text=iPhone+15', 
            rating: 4.5,
            reviews: 1234,
            category: 'Electronics'
        },
        { 
            id: 2, 
            name: 'Samsung 4K Smart TV', 
            price: 799, 
            originalPrice: 999,
            image: 'https://via.placeholder.com/250x200/28a745/ffffff?text=Samsung+TV', 
            rating: 4.3,
            reviews: 856,
            category: 'Electronics'
        },
        { 
            id: 3, 
            name: 'Nike Air Max Shoes', 
            price: 129, 
            originalPrice: 159,
            image: 'https://via.placeholder.com/250x200/dc3545/ffffff?text=Nike+Shoes', 
            rating: 4.7,
            reviews: 2341,
            category: 'Fashion'
        },
        { 
            id: 4, 
            name: 'MacBook Pro M3', 
            price: 1999, 
            originalPrice: 2299,
            image: 'https://via.placeholder.com/250x200/6f42c1/ffffff?text=MacBook+Pro', 
            rating: 4.8,
            reviews: 567,
            category: 'Electronics'
        },
        { 
            id: 5, 
            name: 'Sony Headphones', 
            price: 299, 
            originalPrice: 399,
            image: 'https://via.placeholder.com/250x200/fd7e14/ffffff?text=Sony+Headphones', 
            rating: 4.6,
            reviews: 1876,
            category: 'Electronics'
        },
        { 
            id: 6, 
            name: 'Adidas Sports Jacket', 
            price: 89, 
            originalPrice: 119,
            image: 'https://via.placeholder.com/250x200/20c997/ffffff?text=Adidas+Jacket', 
            rating: 4.4,
            reviews: 432,
            category: 'Fashion'
        },
        { 
            id: 7, 
            name: 'Gaming Mechanical Keyboard', 
            price: 149, 
            originalPrice: 199,
            image: 'https://via.placeholder.com/250x200/e83e8c/ffffff?text=Gaming+Keyboard', 
            rating: 4.5,
            reviews: 789,
            category: 'Electronics'
        },
        { 
            id: 8, 
            name: 'Coffee Maker Deluxe', 
            price: 199, 
            originalPrice: 249,
            image: 'https://via.placeholder.com/250x200/6610f2/ffffff?text=Coffee+Maker', 
            rating: 4.2,
            reviews: 345,
            category: 'Home & Garden'
        }
    ];
    
    return `
        <div class="products-section">
            <h2>Featured Products</h2>
            <div class="products-grid">
                ${products.map(product => `
                    <div class="product-card" data-id="${product.id}" data-category="${product.category}">
                        <div class="product-image">
                            <img src="${product.image}" alt="${product.name}" loading="lazy">
                            ${product.originalPrice > product.price ? 
                                `<div class="discount-badge">
                                    -${Math.round((1 - product.price / product.originalPrice) * 100)}%
                                </div>` : ''
                            }
                        </div>
                        <div class="product-info">
                            <h3>${product.name}</h3>
                            <div class="rating">
                                <span class="stars">${generateStars(product.rating)}</span>
                                <span class="rating-value">${product.rating}</span>
                                <span class="reviews">(${product.reviews})</span>
                            </div>
                            <div class="price-container">
                                <div class="price">$${product.price}</div>
                                ${product.originalPrice > product.price ? 
                                    `<div class="original-price">$${product.originalPrice}</div>` : ''
                                }
                            </div>
                            <button class="add-to-cart" data-product-id="${product.id}">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Helper function to generate star ratings
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '★';
    }
    
    if (hasHalfStar) {
        stars += '☆';
    }
    
    // Fill remaining with empty stars
    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
        stars += '☆';
    }
    
    return stars;
}
