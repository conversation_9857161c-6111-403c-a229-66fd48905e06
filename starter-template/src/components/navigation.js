// Navigation component
export function createNavigation() {
    const categories = [
        { name: 'Electronics', icon: '📱' },
        { name: 'Fashion', icon: '👕' },
        { name: 'Home & Garden', icon: '🏠' },
        { name: 'Sports', icon: '⚽' },
        { name: 'Books', icon: '📚' },
        { name: 'Toys', icon: '🧸' },
        { name: 'Beauty', icon: '💄' },
        { name: 'Automotive', icon: '🚗' }
    ];
    
    return `
        <div class="nav-container">
            <ul class="nav-menu">
                ${categories.map(category => 
                    `<li>
                        <a href="#${category.name.toLowerCase().replace(' & ', '-').replace(' ', '-')}">
                            <span class="nav-icon">${category.icon}</span>
                            ${category.name}
                        </a>
                    </li>`
                ).join('')}
            </ul>
        </div>
    `;
}
