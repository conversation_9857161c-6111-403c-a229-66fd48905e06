// Search functionality
export function initSearch() {
    const searchInput = document.querySelector('.search-bar input');
    const searchBtn = document.querySelector('.search-btn');
    
    if (!searchInput || !searchBtn) {
        console.warn('Search elements not found');
        return;
    }
    
    let searchTimeout;
    
    function performSearch(query) {
        const products = document.querySelectorAll('.product-card');
        const searchQuery = query.toLowerCase().trim();
        
        if (searchQuery === '') {
            // Show all products if search is empty
            products.forEach(product => {
                product.style.display = 'block';
                product.classList.remove('search-highlight');
            });
            updateSearchResults(products.length, products.length);
            return;
        }
        
        let visibleCount = 0;
        
        products.forEach(product => {
            const productName = product.querySelector('h3').textContent.toLowerCase();
            const productCategory = product.dataset.category.toLowerCase();
            
            const nameMatch = productName.includes(searchQuery);
            const categoryMatch = productCategory.includes(searchQuery);
            const isMatch = nameMatch || categoryMatch;
            
            if (isMatch) {
                product.style.display = 'block';
                product.classList.add('search-highlight');
                visibleCount++;
                
                // Highlight matching text
                highlightSearchTerm(product, searchQuery);
            } else {
                product.style.display = 'none';
                product.classList.remove('search-highlight');
            }
        });
        
        updateSearchResults(visibleCount, products.length);
        
        // Scroll to products section
        if (visibleCount > 0) {
            document.querySelector('.products-section').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }
    }
    
    function highlightSearchTerm(productCard, searchTerm) {
        const productName = productCard.querySelector('h3');
        const originalText = productName.textContent;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
        productName.innerHTML = highlightedText;
    }
    
    function updateSearchResults(visibleCount, totalCount) {
        // Remove existing search results message
        const existingMessage = document.querySelector('.search-results-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // Add search results message
        const productsSection = document.querySelector('.products-section');
        const message = document.createElement('div');
        message.className = 'search-results-message';
        message.style.cssText = `
            text-align: center;
            margin: 1rem 0;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
            color: #666;
        `;
        
        if (visibleCount === totalCount) {
            message.textContent = `Showing all ${totalCount} products`;
        } else if (visibleCount > 0) {
            message.textContent = `Found ${visibleCount} product${visibleCount !== 1 ? 's' : ''}`;
        } else {
            message.textContent = 'No products found. Try a different search term.';
            message.style.color = '#e50012';
        }
        
        productsSection.insertBefore(message, productsSection.querySelector('.products-grid'));
    }
    
    function clearSearch() {
        searchInput.value = '';
        performSearch('');
        
        // Remove highlights
        document.querySelectorAll('.product-card h3').forEach(title => {
            title.innerHTML = title.textContent;
        });
    }
    
    // Search button click handler
    searchBtn.addEventListener('click', () => {
        performSearch(searchInput.value);
    });
    
    // Enter key handler
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch(searchInput.value);
        }
    });
    
    // Real-time search with debouncing
    searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch(e.target.value);
        }, 300); // 300ms delay
    });
    
    // Clear search on escape key
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            clearSearch();
        }
    });
    
    // Search suggestions (basic implementation)
    const searchSuggestions = [
        'iPhone', 'Samsung', 'Nike', 'MacBook', 'Sony', 'Adidas', 
        'Gaming', 'Coffee', 'Electronics', 'Fashion', 'Home'
    ];
    
    searchInput.addEventListener('focus', () => {
        if (searchInput.value === '') {
            showSearchSuggestions(searchSuggestions);
        }
    });
    
    function showSearchSuggestions(suggestions) {
        // Remove existing suggestions
        const existingSuggestions = document.querySelector('.search-suggestions');
        if (existingSuggestions) {
            existingSuggestions.remove();
        }
        
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'search-suggestions';
        suggestionsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        `;
        
        suggestions.forEach(suggestion => {
            const suggestionItem = document.createElement('div');
            suggestionItem.textContent = suggestion;
            suggestionItem.style.cssText = `
                padding: 0.5rem 1rem;
                cursor: pointer;
                border-bottom: 1px solid #eee;
                transition: background 0.2s;
            `;
            
            suggestionItem.addEventListener('mouseenter', () => {
                suggestionItem.style.background = '#f8f9fa';
            });
            
            suggestionItem.addEventListener('mouseleave', () => {
                suggestionItem.style.background = 'white';
            });
            
            suggestionItem.addEventListener('click', () => {
                searchInput.value = suggestion;
                performSearch(suggestion);
                suggestionsContainer.remove();
            });
            
            suggestionsContainer.appendChild(suggestionItem);
        });
        
        // Position relative to search bar
        const searchBar = document.querySelector('.search-bar');
        searchBar.style.position = 'relative';
        searchBar.appendChild(suggestionsContainer);
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!searchBar.contains(e.target)) {
                suggestionsContainer.remove();
            }
        }, { once: true });
    }
}
