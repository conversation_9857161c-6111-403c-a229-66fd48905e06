// Shopping cart functionality
class ShoppingCart {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('cart')) || [];
        this.total = 0;
        this.updateTotal();
        this.updateCartUI();
    }
    
    addItem(product) {
        const existingItem = this.items.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.items.push({ ...product, quantity: 1 });
        }
        
        this.updateTotal();
        this.updateCartUI();
        this.saveToStorage();
        this.showNotification(`${product.name} added to cart!`);
    }
    
    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.updateTotal();
        this.updateCartUI();
        this.saveToStorage();
    }
    
    updateQuantity(productId, quantity) {
        const item = this.items.find(item => item.id === productId);
        if (item) {
            item.quantity = Math.max(0, quantity);
            if (item.quantity === 0) {
                this.removeItem(productId);
            } else {
                this.updateTotal();
                this.updateCartUI();
                this.saveToStorage();
            }
        }
    }
    
    updateTotal() {
        this.total = this.items.reduce((sum, item) => 
            sum + (item.price * item.quantity), 0
        );
    }
    
    updateCartUI() {
        const cartCount = document.querySelector('.cart-count');
        const totalItems = this.items.reduce((sum, item) => sum + item.quantity, 0);
        
        if (cartCount) {
            cartCount.textContent = totalItems;
            cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
        }
    }
    
    saveToStorage() {
        localStorage.setItem('cart', JSON.stringify(this.items));
    }
    
    showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'cart-notification';
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#28a745',
            color: 'white',
            padding: '1rem 1.5rem',
            borderRadius: '4px',
            zIndex: '1000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    getItems() {
        return this.items;
    }
    
    getTotal() {
        return this.total;
    }
    
    clear() {
        this.items = [];
        this.total = 0;
        this.updateCartUI();
        this.saveToStorage();
    }
}

// Initialize cart
export const cart = new ShoppingCart();

// Initialize cart functionality
export function initCart() {
    // Add event listeners for "Add to Cart" buttons
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('add-to-cart')) {
            const productCard = e.target.closest('.product-card');
            const productId = parseInt(productCard.dataset.id);
            
            // Get product data from the card
            const productName = productCard.querySelector('h3').textContent;
            const productPriceText = productCard.querySelector('.price').textContent;
            const productPrice = parseFloat(productPriceText.replace('$', ''));
            const productImage = productCard.querySelector('img').src;
            const productCategory = productCard.dataset.category;
            
            const product = {
                id: productId,
                name: productName,
                price: productPrice,
                image: productImage,
                category: productCategory
            };
            
            cart.addItem(product);
            
            // Visual feedback
            const button = e.target;
            const originalText = button.textContent;
            const originalBackground = button.style.background;
            
            button.textContent = 'Adding...';
            button.disabled = true;
            button.style.background = '#ffc107';
            
            setTimeout(() => {
                button.textContent = 'Added!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = originalBackground;
                    button.disabled = false;
                }, 1500);
            }, 500);
        }
    });
    
    // Cart icon click handler (for future cart modal/page)
    const cartIcon = document.querySelector('.cart');
    if (cartIcon) {
        cartIcon.addEventListener('click', (e) => {
            e.preventDefault();
            showCartModal();
        });
    }
}

// Simple cart modal (basic implementation)
function showCartModal() {
    const items = cart.getItems();
    const total = cart.getTotal();
    
    if (items.length === 0) {
        alert('Your cart is empty!');
        return;
    }
    
    let cartContent = 'Your Cart:\n\n';
    items.forEach(item => {
        cartContent += `${item.name} x${item.quantity} - $${(item.price * item.quantity).toFixed(2)}\n`;
    });
    cartContent += `\nTotal: $${total.toFixed(2)}`;
    
    alert(cartContent);
}
