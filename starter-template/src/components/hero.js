// Hero section component
export function createHero() {
    const banners = [
        { 
            id: 1,
            title: 'Summer Sale', 
            subtitle: 'Up to 50% off on electronics', 
            cta: 'Shop Now',
            background: 'linear-gradient(135deg, #e50012, #ff6b6b)'
        },
        { 
            id: 2,
            title: 'New Arrivals', 
            subtitle: 'Latest fashion trends', 
            cta: 'Explore',
            background: 'linear-gradient(135deg, #667eea, #764ba2)'
        },
        { 
            id: 3,
            title: 'Electronics Deal', 
            subtitle: 'Best prices guaranteed', 
            cta: 'Browse',
            background: 'linear-gradient(135deg, #f093fb, #f5576c)'
        }
    ];
    
    return `
        <div class="hero-carousel">
            <div class="carousel-container">
                ${banners.map((banner, index) => `
                    <div class="carousel-slide ${index === 0 ? 'active' : ''}" 
                         style="background: ${banner.background}">
                        <div class="carousel-content">
                            <h2>${banner.title}</h2>
                            <p>${banner.subtitle}</p>
                            <button class="cta-button">${banner.cta}</button>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="carousel-dots">
                ${banners.map((_, index) => 
                    `<button class="dot ${index === 0 ? 'active' : ''}" data-slide="${index}"></button>`
                ).join('')}
            </div>
        </div>
    `;
}

// Carousel functionality
export function initCarousel() {
    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    let currentSlide = 0;
    let slideInterval;
    
    function showSlide(index) {
        // Remove active class from all slides and dots
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));
        
        // Add active class to current slide and dot
        slides[index].classList.add('active');
        dots[index].classList.add('active');
        currentSlide = index;
    }
    
    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }
    
    function startAutoSlide() {
        slideInterval = setInterval(nextSlide, 5000);
    }
    
    function stopAutoSlide() {
        clearInterval(slideInterval);
    }
    
    // Initialize auto-advance carousel
    startAutoSlide();
    
    // Dot navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopAutoSlide();
            showSlide(index);
            startAutoSlide(); // Restart auto-advance
        });
    });
    
    // Pause on hover
    const carousel = document.querySelector('.hero-carousel');
    if (carousel) {
        carousel.addEventListener('mouseenter', stopAutoSlide);
        carousel.addEventListener('mouseleave', startAutoSlide);
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            stopAutoSlide();
            currentSlide = currentSlide === 0 ? slides.length - 1 : currentSlide - 1;
            showSlide(currentSlide);
            startAutoSlide();
        } else if (e.key === 'ArrowRight') {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        }
    });
}
