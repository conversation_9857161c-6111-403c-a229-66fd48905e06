<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flexbox Learning Hub for CS Students</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: slide 20s linear infinite;
        }

        @keyframes slide {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 50px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 25px;
            display: block;
        }

        .feature-card h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .feature-card p {
            color: #5a6c7d;
            line-height: 1.6;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 30px;
            text-align: left;
        }

        .feature-list li {
            color: #27ae60;
            margin-bottom: 10px;
            position: relative;
            padding-left: 30px;
            font-weight: 500;
        }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            font-weight: bold;
            color: #27ae60;
            font-size: 1.2rem;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 18px 35px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(52, 152, 219, 0.4);
            background: linear-gradient(135deg, #2980b9, #3498db);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #27ae60, #229954);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
        }

        .btn.secondary:hover {
            background: linear-gradient(135deg, #229954, #27ae60);
            box-shadow: 0 12px 30px rgba(39, 174, 96, 0.4);
        }

        .learning-path {
            background: #ecf0f1;
            padding: 50px;
            text-align: center;
        }

        .learning-path h3 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 30px;
        }

        .path-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 800px;
            margin: 0 auto;
            flex-wrap: wrap;
            gap: 20px;
        }

        .step {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .step-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px auto;
        }

        .step h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .step p {
            color: #5a6c7d;
            font-size: 0.9rem;
        }

        .arrow {
            font-size: 2rem;
            color: #3498db;
            margin: 0 10px;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px;
            text-align: center;
        }

        .footer h4 {
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .tech-item {
            background: rgba(255,255,255,0.1);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                padding: 30px;
            }
            
            .feature-card {
                padding: 30px;
            }
            
            .path-steps {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
            
            .learning-path {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>🎯 Flexbox Mastery Hub</h1>
                <p>Complete learning system for CSS Flexbox designed specifically for Computer Science students. Master modern web layouts with interactive tutorials and comprehensive guides.</p>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🎮</span>
                <h2>Interactive Tutorial</h2>
                <p>Learn Flexbox through hands-on experimentation with real-time visual feedback and code generation.</p>
                
                <ul class="feature-list">
                    <li>Live property manipulation</li>
                    <li>Real-time code display</li>
                    <li>Visual axis indicators</li>
                    <li>Quick preset layouts</li>
                    <li>Individual item controls</li>
                    <li>Mobile-responsive design</li>
                </ul>
                
                <a href="flexbox-tutorial.html" class="btn">Launch Tutorial</a>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📚</span>
                <h2>Complete Reference Guide</h2>
                <p>Comprehensive documentation covering all Flexbox properties, examples, and common layout patterns.</p>
                
                <ul class="feature-list">
                    <li>All container & item properties</li>
                    <li>Practical code examples</li>
                    <li>Common layout patterns</li>
                    <li>Visual demonstrations</li>
                    <li>Best practices & tips</li>
                    <li>Comparison tables</li>
                </ul>
                
                <a href="flexbox-guide.html" class="btn secondary">View Guide</a>
            </div>
        </div>

        <div class="learning-path">
            <h3>🚀 Your Learning Path</h3>
            <div class="path-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>Read the Basics</h4>
                    <p>Start with the reference guide to understand core concepts and terminology</p>
                </div>
                <div class="arrow">→</div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>Interactive Practice</h4>
                    <p>Use the tutorial to experiment with properties and see immediate results</p>
                </div>
                <div class="arrow">→</div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>Build Projects</h4>
                    <p>Apply your knowledge by creating real layouts and responsive designs</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <h4>🎓 Perfect for CS Students</h4>
            <p>This learning system is designed specifically for computer science students who want to master modern CSS layout techniques. Whether you're building web applications, working on UI/UX projects, or preparing for frontend development roles, these tools will give you the practical skills you need.</p>
            
            <div class="tech-stack">
                <span class="tech-item">HTML5</span>
                <span class="tech-item">CSS3 Flexbox</span>
                <span class="tech-item">JavaScript ES6+</span>
                <span class="tech-item">Responsive Design</span>
                <span class="tech-item">Interactive Learning</span>
            </div>
        </div>
    </div>
</body>
</html>
