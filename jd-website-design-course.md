# 90-Minute Course: Designing an E-commerce Website Like JD.com

## Course Overview
**Target Audience:** CS University Students  
**Duration:** 90 minutes  
**Objective:** Learn to design and build a modern e-commerce website similar to JD.com  
**Prerequisites:** Basic HTML, CSS, JavaScript knowledge  

---

## Course Structure

### Module 1: Understanding JD.com's Design (15 minutes)

#### 1.1 JD.com Analysis (10 minutes)
**Key Design Elements:**
- **Header Navigation**: Logo, search bar, user account, shopping cart
- **Category Navigation**: Horizontal menu with product categories
- **Hero Section**: Large promotional banners/carousel
- **Product Grid**: Featured products with images, prices, ratings
- **Footer**: Links, company info, certifications

**Design Principles Observed:**
- Clean, minimalist layout
- Red and white color scheme (brand colors)
- Grid-based responsive design
- Clear visual hierarchy
- Mobile-first approach

#### 1.2 Technical Architecture Overview (5 minutes)
- Frontend: HTML5, CSS3, JavaScript (likely React/Vue.js)
- Responsive design using CSS Grid/Flexbox
- Image optimization and lazy loading
- Search functionality
- Shopping cart state management

---

### Module 2: Project Setup & Foundation (20 minutes)

#### 2.1 Initialize Project (5 minutes)
```bash
# Create project directory
mkdir jd-clone
cd jd-clone

# Initialize with Vite (modern build tool)
npm create vite@latest . -- --template vanilla
npm install

# Install additional dependencies
npm install lucide-react # for icons
```

#### 2.2 Project Structure (5 minutes)
```
jd-clone/
├── index.html
├── src/
│   ├── main.js
│   ├── style.css
│   ├── components/
│   │   ├── header.js
│   │   ├── navigation.js
│   │   ├── hero.js
│   │   ├── products.js
│   │   └── footer.js
│   └── assets/
│       └── images/
└── package.json
```

#### 2.3 Basic HTML Structure (10 minutes)
**Activity:** Create the main HTML layout
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JD Clone - E-commerce Platform</title>
    <link rel="stylesheet" href="src/style.css">
</head>
<body>
    <header id="header"></header>
    <nav id="navigation"></nav>
    <main>
        <section id="hero"></section>
        <section id="products"></section>
    </main>
    <footer id="footer"></footer>
    <script type="module" src="src/main.js"></script>
</body>
</html>
```

---

### Module 3: Building the Header Component (15 minutes)

#### 3.1 Header HTML Structure (5 minutes)
```html
<!-- header.js component -->
export function createHeader() {
    return `
        <div class="header-container">
            <div class="header-top">
                <div class="logo">
                    <img src="assets/jd-logo.png" alt="JD.com">
                </div>
                <div class="search-bar">
                    <input type="text" placeholder="Search products...">
                    <button class="search-btn">Search</button>
                </div>
                <div class="user-actions">
                    <a href="#" class="login">Login</a>
                    <a href="#" class="cart">
                        <span class="cart-icon">🛒</span>
                        <span class="cart-count">0</span>
                    </a>
                </div>
            </div>
        </div>
    `;
}
```

#### 3.2 Header Styling (10 minutes)
**Activity:** Style the header with CSS
```css
.header-container {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-top {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 2rem;
}

.logo img {
    height: 40px;
}

.search-bar {
    flex: 1;
    display: flex;
    max-width: 500px;
}

.search-bar input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e50012;
    border-right: none;
    outline: none;
}

.search-btn {
    background: #e50012;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
}

.user-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.cart {
    position: relative;
    text-decoration: none;
    color: #333;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e50012;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}
```

---

### Module 4: Navigation Menu (10 minutes)

#### 4.1 Category Navigation (10 minutes)
**Activity:** Create horizontal navigation menu
```javascript
// navigation.js
export function createNavigation() {
    const categories = [
        'Electronics', 'Fashion', 'Home & Garden', 'Sports', 
        'Books', 'Toys', 'Beauty', 'Automotive'
    ];
    
    return `
        <div class="nav-container">
            <ul class="nav-menu">
                ${categories.map(category => 
                    `<li><a href="#${category.toLowerCase()}">${category}</a></li>`
                ).join('')}
            </ul>
        </div>
    `;
}
```

```css
.nav-container {
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.nav-menu {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-menu li a {
    display: block;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #333;
    transition: background 0.3s;
}

.nav-menu li a:hover {
    background: #e50012;
    color: white;
}
```

---

### Module 5: Hero Section with Carousel (15 minutes)

#### 5.1 Hero Banner Structure (8 minutes)
```javascript
// hero.js
export function createHero() {
    const banners = [
        { image: 'banner1.jpg', title: 'Summer Sale', subtitle: 'Up to 50% off' },
        { image: 'banner2.jpg', title: 'New Arrivals', subtitle: 'Latest products' },
        { image: 'banner3.jpg', title: 'Electronics', subtitle: 'Best deals' }
    ];
    
    return `
        <div class="hero-carousel">
            <div class="carousel-container">
                ${banners.map((banner, index) => `
                    <div class="carousel-slide ${index === 0 ? 'active' : ''}">
                        <img src="assets/${banner.image}" alt="${banner.title}">
                        <div class="carousel-content">
                            <h2>${banner.title}</h2>
                            <p>${banner.subtitle}</p>
                            <button class="cta-button">Shop Now</button>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="carousel-dots">
                ${banners.map((_, index) => 
                    `<button class="dot ${index === 0 ? 'active' : ''}" data-slide="${index}"></button>`
                ).join('')}
            </div>
        </div>
    `;
}
```

#### 5.2 Carousel Functionality (7 minutes)
**Activity:** Add JavaScript for carousel
```javascript
export function initCarousel() {
    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    let currentSlide = 0;
    
    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));
        
        slides[index].classList.add('active');
        dots[index].classList.add('active');
        currentSlide = index;
    }
    
    // Auto-advance carousel
    setInterval(() => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }, 5000);
    
    // Dot navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
    });
}
```

---

### Module 6: Product Grid Section (10 minutes)

#### 6.1 Product Data & Structure (10 minutes)
**Activity:** Create product grid with mock data
```javascript
// products.js
export function createProducts() {
    const products = [
        { id: 1, name: 'iPhone 15', price: 999, image: 'iphone.jpg', rating: 4.5 },
        { id: 2, name: 'Samsung TV', price: 799, image: 'tv.jpg', rating: 4.3 },
        { id: 3, name: 'Nike Shoes', price: 129, image: 'shoes.jpg', rating: 4.7 },
        { id: 4, name: 'MacBook Pro', price: 1999, image: 'laptop.jpg', rating: 4.8 },
        // Add more products...
    ];
    
    return `
        <div class="products-section">
            <h2>Featured Products</h2>
            <div class="products-grid">
                ${products.map(product => `
                    <div class="product-card" data-id="${product.id}">
                        <img src="assets/${product.image}" alt="${product.name}">
                        <div class="product-info">
                            <h3>${product.name}</h3>
                            <div class="rating">
                                ${'★'.repeat(Math.floor(product.rating))}
                                <span>${product.rating}</span>
                            </div>
                            <div class="price">$${product.price}</div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}
```

```css
.products-section {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.product-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 1rem;
}

.rating {
    color: #ffa500;
    margin: 0.5rem 0;
}

.price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #e50012;
    margin: 0.5rem 0;
}

.add-to-cart {
    width: 100%;
    background: #e50012;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.add-to-cart:hover {
    background: #c40010;
}
```

---

### Module 7: Shopping Cart Functionality (10 minutes)

#### 7.1 Cart State Management (10 minutes)
**Activity:** Implement shopping cart functionality
```javascript
// cart.js
class ShoppingCart {
    constructor() {
        this.items = [];
        this.total = 0;
    }

    addItem(product) {
        const existingItem = this.items.find(item => item.id === product.id);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.items.push({ ...product, quantity: 1 });
        }

        this.updateTotal();
        this.updateCartUI();
    }

    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.updateTotal();
        this.updateCartUI();
    }

    updateTotal() {
        this.total = this.items.reduce((sum, item) =>
            sum + (item.price * item.quantity), 0
        );
    }

    updateCartUI() {
        const cartCount = document.querySelector('.cart-count');
        const totalItems = this.items.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
    }
}

// Initialize cart
export const cart = new ShoppingCart();

// Add event listeners for "Add to Cart" buttons
export function initCart() {
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('add-to-cart')) {
            const productCard = e.target.closest('.product-card');
            const productId = parseInt(productCard.dataset.id);

            // Get product data (in real app, this would come from API)
            const productName = productCard.querySelector('h3').textContent;
            const productPrice = parseFloat(
                productCard.querySelector('.price').textContent.replace('$', '')
            );

            const product = {
                id: productId,
                name: productName,
                price: productPrice
            };

            cart.addItem(product);

            // Show feedback
            e.target.textContent = 'Added!';
            setTimeout(() => {
                e.target.textContent = 'Add to Cart';
            }, 1000);
        }
    });
}
```

---

### Module 8: Responsive Design (5 minutes)

#### 8.1 Mobile-First CSS (5 minutes)
**Activity:** Add responsive breakpoints
```css
/* Mobile-first responsive design */
@media (max-width: 768px) {
    .header-top {
        flex-direction: column;
        gap: 1rem;
    }

    .search-bar {
        order: 3;
        max-width: 100%;
    }

    .nav-menu {
        flex-direction: column;
    }

    .nav-menu li a {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #ddd;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .carousel-content {
        padding: 1rem;
    }

    .carousel-content h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr 1fr;
    }

    .product-card img {
        height: 150px;
    }

    .header-top {
        padding: 0.5rem;
    }
}
```

---

### Module 9: Advanced Features & Optimization (5 minutes)

#### 9.1 Search Functionality (3 minutes)
```javascript
// search.js
export function initSearch() {
    const searchInput = document.querySelector('.search-bar input');
    const searchBtn = document.querySelector('.search-btn');

    function performSearch(query) {
        const products = document.querySelectorAll('.product-card');

        products.forEach(product => {
            const productName = product.querySelector('h3').textContent.toLowerCase();
            const isMatch = productName.includes(query.toLowerCase());

            product.style.display = isMatch ? 'block' : 'none';
        });
    }

    searchBtn.addEventListener('click', () => {
        performSearch(searchInput.value);
    });

    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch(searchInput.value);
        }
    });
}
```

#### 9.2 Performance Optimization (2 minutes)
```javascript
// main.js - Lazy loading images
export function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}
```

---

### Module 10: Integration & Testing (5 minutes)

#### 10.1 Main Application Assembly (3 minutes)
```javascript
// main.js
import { createHeader } from './components/header.js';
import { createNavigation } from './components/navigation.js';
import { createHero, initCarousel } from './components/hero.js';
import { createProducts } from './components/products.js';
import { initCart } from './components/cart.js';
import { initSearch } from './components/search.js';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Render components
    document.getElementById('header').innerHTML = createHeader();
    document.getElementById('navigation').innerHTML = createNavigation();
    document.getElementById('hero').innerHTML = createHero();
    document.getElementById('products').innerHTML = createProducts();

    // Initialize functionality
    initCarousel();
    initCart();
    initSearch();

    console.log('JD Clone application initialized successfully!');
});
```

#### 10.2 Testing & Debugging (2 minutes)
**Activity:** Test the application
```bash
# Start development server
npm run dev

# Open browser and test:
# 1. Header navigation
# 2. Search functionality
# 3. Carousel auto-advance
# 4. Add to cart functionality
# 5. Responsive design on different screen sizes
```

---

## Course Wrap-up & Next Steps

### What We've Built:
✅ Responsive e-commerce website layout
✅ Interactive product catalog
✅ Shopping cart functionality
✅ Search feature
✅ Mobile-responsive design
✅ Modern JavaScript ES6+ features

### Key Learning Outcomes:
- Understanding of modern web development workflow
- Component-based architecture
- Responsive design principles
- JavaScript DOM manipulation
- CSS Grid and Flexbox
- User experience considerations

### Next Steps for Students:
1. **Add Backend Integration**: Connect to a real API for products
2. **User Authentication**: Implement login/register functionality
3. **Payment Integration**: Add checkout process
4. **Advanced Features**: Filters, sorting, product reviews
5. **Performance**: Implement caching, CDN, image optimization
6. **Testing**: Add unit tests and end-to-end testing
7. **Deployment**: Deploy to Vercel, Netlify, or similar platform

### Additional Resources:
- **MDN Web Docs**: For HTML, CSS, JavaScript reference
- **CSS Grid Guide**: Complete guide to CSS Grid
- **JavaScript ES6+**: Modern JavaScript features
- **React/Vue.js**: For building larger applications
- **Node.js/Express**: For backend development
- **Database Design**: For product catalog management

### Assignment Ideas:
1. Add product filtering by category and price
2. Implement user reviews and ratings
3. Create a wishlist feature
4. Add product comparison functionality
5. Implement dark mode toggle
6. Add animations and micro-interactions

---

**Course Duration:** 90 minutes
**Difficulty Level:** Intermediate
**Technologies Used:** HTML5, CSS3, JavaScript ES6+, Vite
**Final Project:** Fully functional e-commerce website clone
