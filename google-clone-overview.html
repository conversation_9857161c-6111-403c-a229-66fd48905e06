<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Clone Project - CS Student Tutorial</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: slide 20s linear infinite;
        }

        @keyframes slide {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }

        .project-overview {
            padding: 50px;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .overview-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .overview-card .icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .overview-card h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .overview-card p {
            color: #5a6c7d;
            line-height: 1.6;
        }

        .steps-timeline {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 40px;
            margin: 40px 0;
        }

        .steps-timeline h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5rem;
        }

        .timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #3498db;
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 40px;
            width: 50%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            padding-right: 40px;
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 40px;
            text-align: left;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 20px;
            width: 20px;
            height: 20px;
            background: #3498db;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 0 0 4px #3498db;
        }

        .timeline-item:nth-child(odd)::before {
            right: -10px;
        }

        .timeline-item:nth-child(even)::before {
            left: -10px;
        }

        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .timeline-content h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .timeline-content p {
            color: #5a6c7d;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .timeline-content .difficulty {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            margin-top: 10px;
        }

        .difficulty.beginner {
            background: #d4edda;
            color: #155724;
        }

        .difficulty.intermediate {
            background: #fff3cd;
            color: #856404;
        }

        .difficulty.advanced {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 18px 35px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(52, 152, 219, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #27ae60, #229954);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
        }

        .btn.secondary:hover {
            box-shadow: 0 12px 30px rgba(39, 174, 96, 0.4);
        }

        .btn.tertiary {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
        }

        .btn.tertiary:hover {
            box-shadow: 0 12px 30px rgba(231, 76, 60, 0.4);
        }

        .tech-stack {
            background: #ecf0f1;
            padding: 40px;
            text-align: center;
        }

        .tech-stack h3 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 30px;
        }

        .tech-items {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .tech-item {
            background: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            color: #2c3e50;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px;
            text-align: center;
        }

        .footer h4 {
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .footer p {
            opacity: 0.8;
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .overview-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .timeline::before {
                left: 20px;
            }
            
            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 50px !important;
                padding-right: 0 !important;
                text-align: left !important;
            }
            
            .timeline-item::before {
                left: 10px !important;
                right: auto !important;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>🔍 Google Clone Project</h1>
                <p>A comprehensive step-by-step tutorial for CS students to build a pixel-perfect Google homepage clone. Learn modern web development techniques, responsive design, and best practices.</p>
            </div>
        </div>

        <div class="project-overview">
            <div class="overview-grid">
                <div class="overview-card">
                    <span class="icon">🎯</span>
                    <h3>Learning Objectives</h3>
                    <p>Master HTML5 semantic elements, CSS3 advanced styling, Flexbox layout, responsive design principles, and JavaScript interactivity.</p>
                </div>

                <div class="overview-card">
                    <span class="icon">⏱️</span>
                    <h3>Time Investment</h3>
                    <p>Complete the entire project in 4-6 hours. Each step is designed to take 20-40 minutes with clear instructions and checkpoints.</p>
                </div>

                <div class="overview-card">
                    <span class="icon">🎓</span>
                    <h3>Skill Level</h3>
                    <p>Beginner to Intermediate. Perfect for CS students who have basic HTML/CSS knowledge and want to build a real-world project.</p>
                </div>

                <div class="overview-card">
                    <span class="icon">🛠️</span>
                    <h3>Tools Required</h3>
                    <p>Just a text editor (VS Code recommended) and a modern web browser. No frameworks or build tools needed - pure HTML, CSS, and JavaScript.</p>
                </div>
            </div>

            <div class="steps-timeline">
                <h2>📋 Project Timeline</h2>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <h4>Step 1-2: Foundation</h4>
                            <p>Set up HTML structure and add the Google logo with proper centering techniques.</p>
                            <span class="difficulty beginner">Beginner</span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <h4>Step 3-4: Search Interface</h4>
                            <p>Create the iconic search bar with icons and interactive buttons.</p>
                            <span class="difficulty intermediate">Intermediate</span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <h4>Step 5-6: Navigation & Footer</h4>
                            <p>Add header navigation and footer links with proper styling.</p>
                            <span class="difficulty beginner">Beginner</span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <h4>Step 7-8: Responsive Design</h4>
                            <p>Implement mobile-first responsive design and search functionality.</p>
                            <span class="difficulty intermediate">Intermediate</span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <h4>Step 9-10: Advanced Features</h4>
                            <p>Add JavaScript interactivity and final polish for production-ready code.</p>
                            <span class="difficulty advanced">Advanced</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <a href="google-clone-tutorial.html" class="btn">🚀 Start Tutorial</a>
                <a href="final-google-clone.html" class="btn secondary" target="_blank">👀 View Final Result</a>
                <a href="step1-basic-structure.html" class="btn tertiary" target="_blank">📁 Download Starter Files</a>
            </div>
        </div>

        <div class="tech-stack">
            <h3>🛠️ Technologies You'll Master</h3>
            <div class="tech-items">
                <span class="tech-item">HTML5 Semantic Elements</span>
                <span class="tech-item">CSS3 Advanced Styling</span>
                <span class="tech-item">Flexbox Layout</span>
                <span class="tech-item">CSS Grid</span>
                <span class="tech-item">Responsive Design</span>
                <span class="tech-item">JavaScript ES6+</span>
                <span class="tech-item">DOM Manipulation</span>
                <span class="tech-item">Event Handling</span>
            </div>
        </div>

        <div class="footer">
            <h4>🎓 Perfect for CS Students</h4>
            <p>This project is specifically designed for computer science students who want to build practical web development skills. You'll learn industry-standard techniques while creating a pixel-perfect clone of one of the world's most visited websites.</p>
        </div>
    </div>
</body>
</html>
