<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #fff;
            color: #3c4043;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header Navigation */
        .header {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }

        .nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-link {
            color: #3c4043;
            text-decoration: none;
            font-size: 13px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .nav-link:hover {
            background-color: rgba(60, 64, 67, 0.08);
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .apps-menu:hover {
            background-color: rgba(60, 64, 67, 0.08);
        }

        .profile-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #4285f4;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: box-shadow 0.3s ease;
        }

        .profile-btn:hover {
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        }

        /* Main Content */
        .main {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin-top: -60px;
        }

        .logo-container {
            margin-bottom: 30px;
        }

        .google-logo {
            max-width: 272px;
            height: auto;
            display: block;
        }

        .search-container {
            width: 100%;
            max-width: 584px;
            margin-bottom: 30px;
        }

        .search-box {
            display: flex;
            align-items: center;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 10px 16px;
            background: #fff;
            transition: box-shadow 0.3s ease;
            position: relative;
        }

        .search-box:hover {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
        }

        .search-box:focus-within {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: transparent;
        }

        .search-icon, .mic-icon, .camera-icon {
            width: 20px;
            height: 20px;
            fill: #9aa0a6;
            cursor: pointer;
            transition: fill 0.3s ease;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 0 16px;
            color: #3c4043;
        }

        .mic-icon:hover, .camera-icon:hover {
            fill: #4285f4;
        }

        .search-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }

        .search-btn {
            background: #f8f9fa;
            border: 1px solid #f8f9fa;
            border-radius: 4px;
            color: #3c4043;
            font-size: 14px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .search-btn:hover {
            box-shadow: 0 1px 1px rgba(0,0,0,.1);
            background-color: #f1f3f4;
            border: 1px solid #dadce0;
        }

        .language-links {
            font-size: 13px;
            color: #3c4043;
        }

        .language-links a {
            color: #1a0dab;
            text-decoration: none;
            margin: 0 5px;
        }

        .language-links a:hover {
            text-decoration: underline;
        }

        /* Footer */
        .footer {
            background: #f2f2f2;
            border-top: 1px solid #e4e4e4;
        }

        .footer-country {
            padding: 15px 30px;
            border-bottom: 1px solid #dadce0;
            color: #70757a;
            font-size: 15px;
        }

        .footer-links {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
            flex-wrap: wrap;
        }

        .footer-left, .footer-right {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .footer-link {
            color: #70757a;
            text-decoration: none;
            font-size: 14px;
            padding: 5px 0;
        }

        .footer-link:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 10px 15px;
            }

            .nav {
                gap: 10px;
            }

            .google-logo {
                max-width: 200px;
            }

            .search-container {
                max-width: 90%;
            }

            .search-buttons {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .footer-links {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .footer-left, .footer-right {
                justify-content: center;
                gap: 20px;
            }

            .footer-country {
                padding: 15px 20px;
            }
        }

        @media (max-width: 480px) {
            .main {
                padding: 15px;
            }

            .search-box {
                padding: 8px 12px;
            }

            .search-input {
                font-size: 14px;
                padding: 0 10px;
            }

            .search-btn {
                padding: 8px 16px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="nav">
            <a href="#" class="nav-link">Gmail</a>
            <a href="#" class="nav-link">Images</a>
            <svg class="apps-menu" viewBox="0 0 24 24">
                <path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
            </svg>
            <button class="profile-btn">A</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="logo-container">
            <img src="https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png"
                 alt="Google" class="google-logo">
        </div>

        <div class="search-container">
            <div class="search-box">
                <svg class="search-icon" viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                <input type="text" class="search-input" placeholder="">
                <svg class="mic-icon" viewBox="0 0 24 24">
                    <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                </svg>
                <svg class="camera-icon" viewBox="0 0 24 24">
                    <path d="M9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3z"/>
                    <path d="M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zM7 3h10v18H7V3z"/>
                </svg>
            </div>
        </div>

        <div class="search-buttons">
            <button class="search-btn" onclick="performSearch()">Google Search</button>
            <a href="#" class="search-btn">I'm Feeling Lucky</a>
        </div>

        <div class="language-links">
            Google offered in:
            <a href="#">العربية</a>
            <a href="#">中文 (简体)</a>
            <a href="#">Español</a>
            <a href="#">Français</a>
            <a href="#">हिन्दी</a>
            <a href="#">日本語</a>
            <a href="#">Português</a>
            <a href="#">Русский</a>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-country">
            United States
        </div>
        <div class="footer-links">
            <div class="footer-left">
                <a href="#" class="footer-link">About</a>
                <a href="#" class="footer-link">Advertising</a>
                <a href="#" class="footer-link">Business</a>
                <a href="#" class="footer-link">How Search works</a>
            </div>
            <div class="footer-right">
                <a href="#" class="footer-link">Privacy</a>
                <a href="#" class="footer-link">Terms</a>
                <a href="#" class="footer-link">Settings</a>
            </div>
        </div>
    </footer>

    <script>
        // Basic search functionality
        function performSearch() {
            const searchInput = document.querySelector('.search-input');
            const query = searchInput.value.trim();

            if (query) {
                // In a real implementation, this would redirect to Google search results
                // For demo purposes, we'll show an alert
                alert(`Searching for: "${query}"\n\nIn a real implementation, this would redirect to:\nhttps://www.google.com/search?q=${encodeURIComponent(query)}`);

                // Uncomment the line below to actually redirect to Google (for demo purposes)
                // window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
            } else {
                alert('Please enter a search term');
            }
        }

        // Handle Enter key in search input
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Add some interactive features
        document.querySelector('.mic-icon').addEventListener('click', function() {
            alert('Voice search would be implemented here using Web Speech API');
        });

        document.querySelector('.camera-icon').addEventListener('click', function() {
            alert('Image search would be implemented here using camera/file upload');
        });

        // I'm Feeling Lucky functionality
        document.querySelector('.search-buttons a').addEventListener('click', function(e) {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            const query = searchInput.value.trim();

            if (query) {
                alert(`"I'm Feeling Lucky" would take you directly to the first search result for: "${query}"`);
            } else {
                alert('Please enter a search term first');
            }
        });

        // Add focus effect to search input
        const searchInput = document.querySelector('.search-input');
        const searchBox = document.querySelector('.search-box');

        searchInput.addEventListener('focus', function() {
            searchBox.style.boxShadow = '0 2px 5px 1px rgba(64,60,67,.16)';
        });

        searchInput.addEventListener('blur', function() {
            if (!searchInput.value) {
                searchBox.style.boxShadow = '';
            }
        });

        console.log('🔍 Google Clone loaded successfully!');
        console.log('💡 Features implemented:');
        console.log('   • Responsive design');
        console.log('   • Interactive search functionality');
        console.log('   • Keyboard navigation (Enter to search)');
        console.log('   • Voice and image search placeholders');
        console.log('   • I\'m Feeling Lucky functionality');
    </script>
</body>
</html>